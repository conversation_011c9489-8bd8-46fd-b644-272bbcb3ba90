[{"identifier": {"id": "ryanna<PERSON>.laravel-artisan", "uuid": "8a3855a5-f258-48ee-87f9-fa4f7a0875b1"}, "version": "0.0.31", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/ryannaddy.laravel-artisan-0.0.31", "scheme": "file"}, "relativeLocation": "ryannaddy.laravel-artisan-0.0.31", "metadata": {"id": "8a3855a5-f258-48ee-87f9-fa4f7a0875b1", "publisherId": "24a82927-7960-4ab9-84bc-064de264d88b", "publisherDisplayName": "<PERSON>", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "installedTimestamp": 1716369983639, "pinned": false, "source": "gallery"}}, {"identifier": {"id": "xdebug.php-pack", "uuid": "1a9300c9-36a6-44d1-9d09-ada3641b8727"}, "version": "1.0.3", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/xdebug.php-pack-1.0.3", "scheme": "file"}, "relativeLocation": "xdebug.php-pack-1.0.3", "metadata": {"id": "1a9300c9-36a6-44d1-9d09-ada3641b8727", "publisherId": "0162ddc5-cb52-4b88-97e2-5272419cd6cd", "publisherDisplayName": "Xdebug", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "installedTimestamp": 1717315366998, "pinned": false, "source": "gallery"}}, {"identifier": {"id": "zobo.php-intellisense", "uuid": "3eddb68d-a5dd-43fb-be03-6f508d98fe97"}, "version": "1.3.3", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/zobo.php-intellisense-1.3.3", "scheme": "file"}, "relativeLocation": "zobo.php-intellisense-1.3.3", "metadata": {"id": "3eddb68d-a5dd-43fb-be03-6f508d98fe97", "publisherId": "2a621670-b579-442f-86a8-3502b16f8806", "publisherDisplayName": "<PERSON><PERSON>", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "installedTimestamp": 1717315367001, "pinned": false, "source": "gallery"}}, {"identifier": {"id": "rifi2k.format-html-in-php", "uuid": "6b8be8ff-4ed6-4534-99e6-c37b7a82aea6"}, "version": "1.7.0", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/rifi2k.format-html-in-php-1.7.0", "scheme": "file"}, "relativeLocation": "rifi2k.format-html-in-php-1.7.0", "metadata": {"id": "6b8be8ff-4ed6-4534-99e6-c37b7a82aea6", "publisherId": "0a5f1674-fcbc-414d-ad46-2fbc8ab6f227", "publisherDisplayName": "rifi2k", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "installedTimestamp": 1717315380435, "pinned": false, "source": "gallery"}}, {"identifier": {"id": "mehedidracula.php-namespace-resolver", "uuid": "651dce1f-eda8-4a5b-aa26-0d1266de8ed1"}, "version": "1.1.9", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/mehedidracula.php-namespace-resolver-1.1.9", "scheme": "file"}, "relativeLocation": "mehedidracula.php-namespace-resolver-1.1.9", "metadata": {"id": "651dce1f-eda8-4a5b-aa26-0d1266de8ed1", "publisherId": "e6a54efe-9186-460e-b45b-8da24a8b8d5f", "publisherDisplayName": "<PERSON><PERSON><PERSON>", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "installedTimestamp": 1717315386760, "pinned": false, "source": "gallery"}}, {"identifier": {"id": "atishay-jain.all-autocomplete", "uuid": "55c9a325-d650-4ab2-a952-3f3c6faba089"}, "version": "0.0.26", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/atishay-jain.all-autocomplete-0.0.26", "scheme": "file"}, "relativeLocation": "atishay-jain.all-autocomplete-0.0.26", "metadata": {"id": "55c9a325-d650-4ab2-a952-3f3c6faba089", "publisherId": "366818c6-00cf-4682-821a-f16ea083ff17", "publisherDisplayName": "<PERSON><PERSON><PERSON>", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "installedTimestamp": 1717315523398, "pinned": false, "source": "gallery"}}, {"identifier": {"id": "anbuselvanrocky.bootstrap5-vscode", "uuid": "905e2fea-1455-4a73-9319-57b046ce5543"}, "version": "0.4.4", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/anbuselvanrocky.bootstrap5-vscode-0.4.4", "scheme": "file"}, "relativeLocation": "anbuselvanrocky.bootstrap5-vscode-0.4.4", "metadata": {"id": "905e2fea-1455-4a73-9319-57b046ce5543", "publisherId": "75a167ce-5fba-4c7b-9bc8-c78e0bb7a8a6", "publisherDisplayName": "<PERSON><PERSON><PERSON><PERSON>", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "installedTimestamp": 1717315601406, "pinned": false, "source": "gallery"}}, {"identifier": {"id": "xabikos.javascriptsnippets", "uuid": "a2cec723-5349-460d-9de9-0fd1f8d3456f"}, "version": "1.8.0", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/xabikos.javascriptsnippets-1.8.0", "scheme": "file"}, "relativeLocation": "xabikos.javascriptsnippets-1.8.0", "metadata": {"id": "a2cec723-5349-460d-9de9-0fd1f8d3456f", "publisherId": "8961c3fe-3ec6-429d-886a-50b5af362a88", "publisherDisplayName": "<PERSON>ara<PERSON><PERSON>", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "installedTimestamp": 1717315845646, "pinned": false, "source": "gallery"}}, {"identifier": {"id": "zignd.html-css-class-completion", "uuid": "7b71fc1b-190d-4f7d-95d1-93e422649b0a"}, "version": "1.20.0", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/zignd.html-css-class-completion-1.20.0", "scheme": "file"}, "relativeLocation": "zignd.html-css-class-completion-1.20.0", "metadata": {"id": "7b71fc1b-190d-4f7d-95d1-93e422649b0a", "publisherId": "20bf23ac-66a2-4404-9047-4b8215047b8b", "publisherDisplayName": "Zignd", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "installedTimestamp": 1717315897249, "pinned": false, "source": "gallery"}}, {"identifier": {"id": "visualstudioexptteam.vscodeintellicode-completions", "uuid": "b8febe71-368d-482e-96f5-6f53d03e640b"}, "version": "2.0.1", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/visualstudioexptteam.vscodeintellicode-completions-2.0.1", "scheme": "file"}, "relativeLocation": "visualstudioexptteam.vscodeintellicode-completions-2.0.1", "metadata": {"installedTimestamp": 1732886406977, "pinned": false, "source": "gallery", "id": "b8febe71-368d-482e-96f5-6f53d03e640b", "publisherId": "e8db1608-52e8-4d8d-92a6-779c5db302a9", "publisherDisplayName": "Microsoft", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "onecentlin.laravel-extension-pack", "uuid": "970bf208-88ee-472e-a6cf-63ce54817acc"}, "version": "1.3.0", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/onecentlin.laravel-extension-pack-1.3.0", "scheme": "file"}, "relativeLocation": "onecentlin.laravel-extension-pack-1.3.0", "metadata": {"installedTimestamp": 1735230255316, "pinned": false, "source": "gallery", "id": "970bf208-88ee-472e-a6cf-63ce54817acc", "publisherId": "ae5a5701-dff3-41f0-bdb1-adbf4a18997b", "publisherDisplayName": "<PERSON><PERSON>", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "mikestead.dotenv", "uuid": "532533c9-a894-4a58-9eee-bbfbe7c06f71"}, "version": "1.0.1", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/mikestead.dotenv-1.0.1", "scheme": "file"}, "relativeLocation": "mikestead.dotenv-1.0.1", "metadata": {"installedTimestamp": 1735230255320, "pinned": false, "source": "gallery", "id": "532533c9-a894-4a58-9eee-bbfbe7c06f71", "publisherId": "b6aa9b98-56e5-4846-b7d2-600d45fc63fc", "publisherDisplayName": "mikestead", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "naoray.laravel-goto-components", "uuid": "c2ef8fe6-9029-4ed9-a7bc-20fd59b327a6"}, "version": "1.2.0", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/naoray.laravel-goto-components-1.2.0", "scheme": "file"}, "relativeLocation": "naoray.laravel-goto-components-1.2.0", "metadata": {"installedTimestamp": 1735230255323, "pinned": false, "source": "gallery", "id": "c2ef8fe6-9029-4ed9-a7bc-20fd59b327a6", "publisherId": "7f58b51d-03e1-4554-a4d8-381c4323978a", "publisherDisplayName": "naoray", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "glitchbl.laravel-create-view", "uuid": "285b59bb-7629-432e-9c32-417b90e5fbc2"}, "version": "0.0.6", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/glitchbl.laravel-create-view-0.0.6", "scheme": "file"}, "relativeLocation": "glitchbl.laravel-create-view-0.0.6", "metadata": {"installedTimestamp": 1735230255326, "pinned": false, "source": "gallery", "id": "285b59bb-7629-432e-9c32-417b90e5fbc2", "publisherId": "789bdcd8-97c8-4986-8936-079c1461deb9", "publisherDisplayName": "glitchbl", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "codingyu.laravel-goto-view", "uuid": "0b86e0a5-d2ef-48d8-9a1a-73f96e65f26e"}, "version": "1.3.11", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/codingyu.laravel-goto-view-1.3.11", "scheme": "file"}, "relativeLocation": "codingyu.laravel-goto-view-1.3.11", "metadata": {"installedTimestamp": 1735230255321, "pinned": false, "source": "gallery", "id": "0b86e0a5-d2ef-48d8-9a1a-73f96e65f26e", "publisherId": "cb0de273-0bdf-4cd8-aa9e-ae0a5627aca2", "publisherDisplayName": "<PERSON><PERSON>", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "ihunte.laravel-blade-wrapper", "uuid": "8cd88401-57df-4c5d-bd32-8d7e1877b88a"}, "version": "1.0.2", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/ihunte.laravel-blade-wrapper-1.0.2", "scheme": "file"}, "relativeLocation": "ihunte.laravel-blade-wrapper-1.0.2", "metadata": {"installedTimestamp": 1735230255324, "pinned": false, "source": "gallery", "id": "8cd88401-57df-4c5d-bd32-8d7e1877b88a", "publisherId": "23046211-493d-484e-b677-36c91fffbc2b", "publisherDisplayName": "IHunte", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "hoovercj.vscode-power-mode", "uuid": "a27ff63d-ff11-478d-b1ab-19aa1ef19882"}, "version": "3.0.2", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/hoovercj.vscode-power-mode-3.0.2", "scheme": "file"}, "relativeLocation": "hoovercj.vscode-power-mode-3.0.2", "metadata": {"installedTimestamp": 1735274248759, "pinned": false, "source": "gallery", "id": "a27ff63d-ff11-478d-b1ab-19aa1ef19882", "publisherId": "9ce6dd66-61b2-41ff-8680-39cba0813c3d", "publisherDisplayName": "<PERSON>", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "amiralizadeh9480.laravel-extra-intellisense", "uuid": "b52072d1-e507-4261-8935-f4d91a055d7d"}, "version": "0.7.2", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/amiralizadeh9480.laravel-extra-intellisense-0.7.2", "scheme": "file"}, "relativeLocation": "amiralizadeh9480.laravel-extra-intellisense-0.7.2", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1737388049033, "pinned": false, "source": "gallery", "id": "b52072d1-e507-4261-8935-f4d91a055d7d", "publisherId": "408616ef-0321-4001-b2f0-ae74e0f71aa2", "publisherDisplayName": "amir", "targetPlatform": "undefined", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "ecmel.vscode-html-css", "uuid": "aaee577c-f062-495a-9816-0cbd442f1d25"}, "version": "2.0.13", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/ecmel.vscode-html-css-2.0.13", "scheme": "file"}, "relativeLocation": "ecmel.vscode-html-css-2.0.13", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1737947384651, "pinned": false, "source": "gallery", "id": "aaee577c-f062-495a-9816-0cbd442f1d25", "publisherId": "7aa35068-d7c4-4220-b9c1-10360795fd5f", "publisherDisplayName": "ecmel", "targetPlatform": "undefined", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "visualstudioexptteam.vscodeintellicode", "uuid": "876e8f93-74d0-4f4f-91b7-34a09f19f444"}, "version": "1.3.2", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/visualstudioexptteam.vscodeintellicode-1.3.2", "scheme": "file"}, "relativeLocation": "visualstudioexptteam.vscodeintellicode-1.3.2", "metadata": {"installedTimestamp": 1738381576092, "pinned": false, "source": "gallery", "id": "876e8f93-74d0-4f4f-91b7-34a09f19f444", "publisherId": "e8db1608-52e8-4d8d-92a6-779c5db302a9", "publisherDisplayName": "Microsoft", "targetPlatform": "undefined", "updated": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "shufo.vscode-blade-formatter", "uuid": "68a2e971-8ae5-493b-9c34-f4233fb14e40"}, "version": "0.24.6", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/shufo.vscode-blade-formatter-0.24.6", "scheme": "file"}, "relativeLocation": "shufo.vscode-blade-formatter-0.24.6", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1739760913419, "pinned": false, "source": "gallery", "id": "68a2e971-8ae5-493b-9c34-f4233fb14e40", "publisherId": "d48faf8e-4a0c-4f2d-a547-d2618494c84d", "publisherDisplayName": "<PERSON><PERSON><PERSON>", "targetPlatform": "undefined", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "pranaygp.vscode-css-peek", "uuid": "0b8f8d63-11a2-4194-969c-ca7488b3413a"}, "version": "4.4.3", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/pranaygp.vscode-css-peek-4.4.3", "scheme": "file"}, "relativeLocation": "pranaygp.vscode-css-peek-4.4.3", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1741268805437, "pinned": false, "source": "gallery", "id": "0b8f8d63-11a2-4194-969c-ca7488b3413a", "publisherId": "3975bc66-f8bb-46a9-b8b6-430f013e4fa5", "publisherDisplayName": "<PERSON><PERSON><PERSON>", "targetPlatform": "undefined", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "yzhang.markdown-all-in-one", "uuid": "98790d67-10fa-497c-9113-f6c7489207b2"}, "version": "3.6.3", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/yzhang.markdown-all-in-one-3.6.3", "scheme": "file"}, "relativeLocation": "yzhang.markdown-all-in-one-3.6.3", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1741572989868, "pinned": false, "source": "gallery", "id": "98790d67-10fa-497c-9113-f6c7489207b2", "publisherId": "36c8b41c-6ef6-4bf5-a5b7-65bef29b606f", "publisherDisplayName": "<PERSON>", "targetPlatform": "undefined", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "onecentlin.laravel-blade", "uuid": "3b58a227-618a-4b57-a06b-6984a2a8d1ba"}, "version": "1.37.0", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/onecentlin.laravel-blade-1.37.0", "scheme": "file"}, "relativeLocation": "onecentlin.laravel-blade-1.37.0", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1742626647328, "pinned": false, "source": "gallery", "id": "3b58a227-618a-4b57-a06b-6984a2a8d1ba", "publisherId": "ae5a5701-dff3-41f0-bdb1-adbf4a18997b", "publisherDisplayName": "<PERSON><PERSON>", "targetPlatform": "undefined", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "onecentlin.laravel5-snippets", "uuid": "050d4900-7979-4d10-b7aa-e274f6ae8730"}, "version": "1.19.1", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/onecentlin.laravel5-snippets-1.19.1", "scheme": "file"}, "relativeLocation": "onecentlin.laravel5-snippets-1.19.1", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1743247634154, "pinned": false, "source": "gallery", "id": "050d4900-7979-4d10-b7aa-e274f6ae8730", "publisherId": "ae5a5701-dff3-41f0-bdb1-adbf4a18997b", "publisherDisplayName": "<PERSON><PERSON>", "targetPlatform": "undefined", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "bmewburn.vscode-intelephense-client", "uuid": "ffda6552-0ad5-431b-a4fa-0905a7e4f3f0"}, "version": "1.14.4", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/bmewburn.vscode-intelephense-client-1.14.4", "scheme": "file"}, "relativeLocation": "bmewburn.vscode-intelephense-client-1.14.4", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1743510188445, "pinned": false, "source": "gallery", "id": "ffda6552-0ad5-431b-a4fa-0905a7e4f3f0", "publisherId": "3209eeb0-2b1a-4484-8c83-2627b29db88e", "publisherDisplayName": "<PERSON>", "targetPlatform": "undefined", "updated": true, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "xdebug.php-debug", "uuid": "044b4b31-2969-4af3-895d-855433b8b46d"}, "version": "1.36.1", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/xdebug.php-debug-1.36.1", "scheme": "file"}, "relativeLocation": "xdebug.php-debug-1.36.1", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1745673351964, "pinned": false, "source": "gallery", "id": "044b4b31-2969-4af3-895d-855433b8b46d", "publisherId": "0162ddc5-cb52-4b88-97e2-5272419cd6cd", "publisherDisplayName": "Xdebug", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "pgl.laravel-jump-controller", "uuid": "c20ee256-1ef8-46a2-99cd-0cb62a42d2ba"}, "version": "0.0.34", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/pgl.laravel-jump-controller-0.0.34", "scheme": "file"}, "relativeLocation": "pgl.laravel-jump-controller-0.0.34", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1747112247218, "pinned": false, "source": "gallery", "id": "c20ee256-1ef8-46a2-99cd-0cb62a42d2ba", "publisherId": "faf0bed4-3c76-4ae9-95f7-3a5ec89db1e1", "publisherDisplayName": "pgl", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "blackboxapp.blackbox", "uuid": "29f905db-7b5e-4c4e-9295-02bf72a065d0"}, "version": "2.8.43", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/blackboxapp.blackbox-2.8.43", "scheme": "file"}, "relativeLocation": "blackboxapp.blackbox-2.8.43", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1747393467642, "pinned": false, "source": "gallery", "id": "29f905db-7b5e-4c4e-9295-02bf72a065d0", "publisherId": "748d9dfd-c7c4-4ce2-b19d-7e4df254c5e8", "publisherDisplayName": "BLACKBOXAI", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "monokai.theme-monokai-pro-vscode", "uuid": "f5d7ffda-c1d6-4070-ba80-803c705a1ee6"}, "version": "2.0.7", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/monokai.theme-monokai-pro-vscode-2.0.7", "scheme": "file"}, "relativeLocation": "monokai.theme-monokai-pro-vscode-2.0.7", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1749147683943, "pinned": false, "source": "gallery", "id": "f5d7ffda-c1d6-4070-ba80-803c705a1ee6", "publisherId": "f1aa9b60-e034-4838-a0dc-4a6a074cf48e", "publisherDisplayName": "monokai", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "editorconfig.editorconfig", "uuid": "f60a60a6-95ba-42d4-b41c-3d24c1b89588"}, "version": "0.17.4", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/editorconfig.editorconfig-0.17.4", "scheme": "file"}, "relativeLocation": "editorconfig.editorconfig-0.17.4", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1749147683867, "pinned": false, "source": "gallery", "id": "f60a60a6-95ba-42d4-b41c-3d24c1b89588", "publisherId": "1ed869e4-8588-4af4-a51e-9c1c86b034b9", "publisherDisplayName": "EditorConfig", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "devsense.composer-php-vscode", "uuid": "eed2a934-7bb6-4a5b-a29d-353077efd839"}, "version": "1.59.17515", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/devsense.composer-php-vscode-1.59.17515", "scheme": "file"}, "relativeLocation": "devsense.composer-php-vscode-1.59.17515", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751094617114, "pinned": false, "source": "gallery", "id": "eed2a934-7bb6-4a5b-a29d-353077efd839", "publisherId": "5db849bc-0940-444f-8936-5d2cdbb81abb", "publisherDisplayName": "DEVSENSE", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "devsense.profiler-php-vscode", "uuid": "6e4e2904-96c2-4c5c-8b78-23da062f2f9e"}, "version": "1.59.17515", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/devsense.profiler-php-vscode-1.59.17515", "scheme": "file"}, "relativeLocation": "devsense.profiler-php-vscode-1.59.17515", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751094617099, "pinned": false, "source": "gallery", "id": "6e4e2904-96c2-4c5c-8b78-23da062f2f9e", "publisherId": "5db849bc-0940-444f-8936-5d2cdbb81abb", "publisherDisplayName": "DEVSENSE", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "ms-dotnettools.vscode-dotnet-runtime", "uuid": "1aab81a1-b3d9-4aef-976b-577d5d90fe3f"}, "version": "2.3.6", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/ms-dotnettools.vscode-dotnet-runtime-2.3.6", "scheme": "file"}, "relativeLocation": "ms-dotnettools.vscode-dotnet-runtime-2.3.6", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751094617165, "pinned": false, "source": "gallery", "id": "1aab81a1-b3d9-4aef-976b-577d5d90fe3f", "publisherId": "d05e23de-3974-4ff0-8d47-23ee77830092", "publisherDisplayName": "Microsoft", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "devsense.phptools-vscode", "uuid": "93916db4-e944-4c74-b68e-47f2834e9e93"}, "version": "1.59.17515", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/devsense.phptools-vscode-1.59.17515-win32-x64", "scheme": "file"}, "relativeLocation": "devsense.phptools-vscode-1.59.17515-win32-x64", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751094617166, "pinned": false, "source": "gallery", "id": "93916db4-e944-4c74-b68e-47f2834e9e93", "publisherId": "5db849bc-0940-444f-8936-5d2cdbb81abb", "publisherDisplayName": "DEVSENSE", "targetPlatform": "win32-x64", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "ms-vscode.cpptools", "uuid": "690b692e-e8a9-493f-b802-8089d50ac1b2"}, "version": "1.26.3", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/ms-vscode.cpptools-1.26.3-win32-x64", "scheme": "file"}, "relativeLocation": "ms-vscode.cpptools-1.26.3-win32-x64", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751094617825, "pinned": false, "source": "gallery", "id": "690b692e-e8a9-493f-b802-8089d50ac1b2", "publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherDisplayName": "Microsoft", "targetPlatform": "win32-x64", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "anthropic.claude-code", "uuid": "3c13ae49-babe-45fe-8c48-5e45077a62bf"}, "version": "1.0.31", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/anthropic.claude-code-1.0.31", "scheme": "file"}, "relativeLocation": "anthropic.claude-code-1.0.31", "metadata": {"installedTimestamp": 1751094652369, "pinned": false, "source": "gallery", "id": "3c13ae49-babe-45fe-8c48-5e45077a62bf", "publisherId": "89769da0-cc4b-40b0-8216-93ffb5a96b56", "publisherDisplayName": "Anthropic", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "hossaini.bootstrap-intellisense", "uuid": "7846dad3-dea1-40d3-a7f2-da368838d5fb"}, "version": "3.0.4", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/hossaini.bootstrap-intellisense-3.0.4", "scheme": "file"}, "relativeLocation": "hossaini.bootstrap-intellisense-3.0.4", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751773237856, "pinned": false, "source": "gallery", "id": "7846dad3-dea1-40d3-a7f2-da368838d5fb", "publisherId": "5aea1c1b-e108-4f19-8e0e-4f1029e56107", "publisherDisplayName": "<PERSON><PERSON><PERSON>", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "damms005.devdb", "uuid": "d4571f36-74a5-4d59-87bc-8351122c8915"}, "version": "2.8.8", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/damms005.devdb-2.8.8-win32-x64", "scheme": "file"}, "relativeLocation": "damms005.devdb-2.8.8-win32-x64", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751773237953, "pinned": false, "source": "gallery", "id": "d4571f36-74a5-4d59-87bc-8351122c8915", "publisherId": "475a3176-6c20-43e7-86fb-63f08382bec9", "publisherDisplayName": "<PERSON><PERSON><PERSON>", "targetPlatform": "win32-x64", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "blackboxapp.blackboxagent", "uuid": "682f8a6c-d419-400a-9009-37b7b00680f0"}, "version": "3.3.33", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/blackboxapp.blackboxagent-3.3.33", "scheme": "file"}, "relativeLocation": "blackboxapp.blackboxagent-3.3.33", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751773238000, "pinned": false, "source": "gallery", "id": "682f8a6c-d419-400a-9009-37b7b00680f0", "publisherId": "748d9dfd-c7c4-4ce2-b19d-7e4df254c5e8", "publisherDisplayName": "BLACKBOXAI", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "ms-dotnettools.csharp", "uuid": "d0bfc4ab-1d3a-4487-8782-7cf6027b4fff"}, "version": "2.84.19", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/ms-dotnettools.csharp-2.84.19-win32-x64", "scheme": "file"}, "relativeLocation": "ms-dotnettools.csharp-2.84.19-win32-x64", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1751773238668, "pinned": false, "source": "gallery", "id": "d0bfc4ab-1d3a-4487-8782-7cf6027b4fff", "publisherId": "d05e23de-3974-4ff0-8d47-23ee77830092", "publisherDisplayName": "Microsoft", "targetPlatform": "win32-x64", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "google.geminicodeassist", "uuid": "51643712-2cb2-4384-b7cc-d55b01b8274b"}, "version": "2.39.0", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/google.geminicodeassist-2.39.0", "scheme": "file"}, "relativeLocation": "google.geminicodeassist-2.39.0", "metadata": {"installedTimestamp": 1751779431032, "pinned": false, "source": "gallery", "id": "51643712-2cb2-4384-b7cc-d55b01b8274b", "publisherId": "93a45bde-b507-401c-9deb-7a098ebcded8", "publisherDisplayName": "Google", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "devsense.intelli-php-vscode", "uuid": "f83b9c6c-24dd-435c-a088-daff1769b0b8"}, "version": "0.12.17635", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/devsense.intelli-php-vscode-0.12.17635-win32-x64", "scheme": "file"}, "relativeLocation": "devsense.intelli-php-vscode-0.12.17635-win32-x64", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1752037256409, "pinned": false, "source": "gallery", "id": "f83b9c6c-24dd-435c-a088-daff1769b0b8", "publisherId": "5db849bc-0940-444f-8936-5d2cdbb81abb", "publisherDisplayName": "DEVSENSE", "targetPlatform": "win32-x64", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "tabnine.tabnine-vscode", "uuid": "75da638c-c45a-44ea-aa3b-8570a3559810"}, "version": "3.294.0", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/tabnine.tabnine-vscode-3.294.0", "scheme": "file"}, "relativeLocation": "tabnine.tabnine-vscode-3.294.0", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1752080452846, "pinned": false, "source": "gallery", "id": "75da638c-c45a-44ea-aa3b-8570a3559810", "publisherId": "1924b661-7c19-45d9-9800-edeb32848fd7", "publisherDisplayName": "TabNine", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "ms-dotnettools.bla<PERSON><PERSON>m-companion", "uuid": "4dbd0c1f-818d-4c90-b962-213c98d8a656"}, "version": "1.1.4", "location": {"$mid": 1, "path": "/c:/Users/<USER>/.vscode/extensions/ms-dotnettools.blazorwasm-companion-1.1.4", "scheme": "file"}, "relativeLocation": "ms-dotnettools.blazorwasm-companion-1.1.4", "metadata": {"installedTimestamp": 1752119189930, "pinned": false, "source": "gallery", "id": "4dbd0c1f-818d-4c90-b962-213c98d8a656", "publisherId": "d05e23de-3974-4ff0-8d47-23ee77830092", "publisherDisplayName": "Microsoft", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "augment.vscode-augment"}, "version": "0.496.2", "location": {"$mid": 1, "fsPath": "c:\\Users\\<USER>\\.vscode\\extensions\\augment.vscode-augment-0.496.2", "_sep": 1, "external": "file:///c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.496.2", "path": "/c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.496.2", "scheme": "file"}, "relativeLocation": "augment.vscode-augment-0.496.2", "metadata": {"installedTimestamp": 1752119763676, "pinned": false, "source": "gallery", "id": "fc0e137d-e132-47ed-9455-c4636fa5b897", "publisherId": "7814b14b-491a-4e83-83ac-9222fa835050", "publisherDisplayName": "Augment Computing", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}]