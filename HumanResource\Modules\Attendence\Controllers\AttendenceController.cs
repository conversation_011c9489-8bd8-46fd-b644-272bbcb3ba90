using HumanResource.Core.Helpers.Attributes;
using Microsoft.AspNetCore.Mvc;
using HumanResource.Modules.Attendence.Models.Entities;
using Microsoft.EntityFrameworkCore;
using HumanResource.Modules.Attendence.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Modules.Shared.Services;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Employees.Models.Entities;

namespace HumanResource.Modules.Attendence.Controllers;


[Area("Attendence")]
[Route("Attendence")]
[Can("attendence")]
public class AttendenceController : BaseController
{

    public AttendenceViewModel _v;
    private readonly EmailService _emailService;
    private readonly ILogger<AttendenceController> _logger;

    public AttendenceController(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor, 
        App<PERSON>elper helper,
        EmailService emailService,
        ILogger<AttendenceController> logger)
        : base(context, bccontext, httpContextAccessor, helper)
    {
        _db = context;
        _v = new AttendenceViewModel(context, httpContextAccessor, helper);
        _emailService = emailService;
        _logger = logger;

        _v.Page.Active = "attendence";
        
        // Register email template if it doesn't exist
        RegisterAttendanceWarningTemplate();
    }
    
    private void RegisterAttendanceWarningTemplate()
    {
        // This method is now simplified since EmailService directly checks for templates in the file system
        const string templateName = "attendance.warning";
        
        // We can optionally log that we're relying on a file system template
        try {
            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "Modules", "Shared", "Views", "EmailTemplates", $"{templateName}.cshtml");
            if (!System.IO.File.Exists(templatePath))
            {
                // Log a warning if the template file doesn't exist
                _logger.LogWarning($"Email template file '{templateName}.cshtml' not found in EmailTemplates directory");
            }
        }
        catch (Exception ex) {
            _logger.LogError(ex, $"Error checking for email template: {ex.Message}");
        }
    }

    public IActionResult Index()
    {
        return View(_v);
    }


    public IActionResult Schedules()
    {
        return View(_v);
    }

    [HttpGet("DailyReport")]
    public IActionResult DailyReport(DateTime? date)
    {
        date ??= DateTime.Today;
        
        _v.SelectedDate = date.Value;
        _v.DailyAttendance = _db.EmpWorkHours
            .Where(w => w.Day.Date == date.Value.Date)
            .OrderBy(w => w.EmpNo)
            .ToList();
            
        // Get the list of all employees
        _v.VempDtls = _db.VempDtls.Where(v => v.DesgnHierLevel > 4).ToList();
        
        return View(_v);
    }
    
    [HttpGet("MonthlyNonAttendanceReport")]
    public IActionResult MonthlyNonAttendanceReport(int? month, int? year)
    {
        month ??= DateTime.Today.Month;
        year ??= DateTime.Today.Year;
        
        _v.SelectedMonth = month.Value;
        _v.SelectedYear = year.Value;
        
        // Get start and end date of the selected month
        var startDate = new DateTime(year.Value, month.Value, 1);
        var endDate = startDate.AddMonths(1).AddDays(-1);
        
        // Get all employees
        var allEmployees = _db.VempDtls.Where(v => v.DesgnHierLevel > 4).ToList();
        
        // Get all attendance records for the month
        var attendanceRecords = _db.EmpWorkHours
            .Where(w => w.Day.Date >= startDate && w.Day.Date <= endDate)
            .ToList();
        
        // Group attendance records by date
        var attendanceByDate = new Dictionary<DateTime, List<int>>();
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var attendedEmployees = attendanceRecords
                .Where(a => a.Day.Date == date.Date)
                .Select(a => a.EmpNo)
                .ToList();
                
            attendanceByDate[date] = attendedEmployees;
        }
        
        _v.AttendanceByDate = attendanceByDate;
        _v.VempDtls = allEmployees;
        
        return View(_v);
    }
    
    [HttpGet("My")]
    public IActionResult MyAttendance(int? month, int? year)
    {
        month ??= DateTime.Today.Month;
        year ??= DateTime.Today.Year;
        
        _v.SelectedMonth = month.Value;
        _v.SelectedYear = year.Value;
        _v.Page.Active="";
        
        // Get the logged-in employee
        var employeeNo = Auth().EmpNo;
        
        // Get start and end date of the selected month
        var startDate = new DateTime(year.Value, month.Value, 1);
        var endDate = startDate.AddMonths(1).AddDays(-1);
        
        // Get attendance records for the employee during the selected month
        _v.DailyAttendance = _db.EmpWorkHours
            .Where(w => w.EmpNo == employeeNo && w.Day.Date >= startDate && w.Day.Date <= endDate)
            .OrderBy(w => w.Day)
            .ToList();
            
        // Get employee details
        _v.Employee = _db.VempDtls.FirstOrDefault(e => e.EmpNo == employeeNo);
        
        // Get excuses for the month
        _v.MonthlyExcuses = _db.Absents
            .Where(e => e.EmpNo == employeeNo && e.OrderDate >= startDate && e.OrderDate <= endDate)
            .OrderBy(e => e.OrderDate)
            .ToList();
            
        // Get leave information for the month
        _v.LeaveApplications = _db.TleaveAplTxs
            .Where(l => l.EmpNo == employeeNo && 
                  l.CancelFlag == 0 && // Not canceled
                  (l.LeaveStartDate >= startDate && l.LeaveStartDate <= endDate || 
                   l.LeaveEndDate >= startDate && l.LeaveEndDate <= endDate ||
                   l.LeaveStartDate <= startDate && l.LeaveEndDate >= endDate))
            .Include(l => l.TleaveCode) // Include the leave code description
            .ToList();
            
        // Get all leave codes for reference
        _v.LeaveCodes = _db.TleaveCodes.ToList();
            
        // Calculate total excuse duration for the month
        _v.TotalExcuseDuration = 0;
        foreach (var excuse in _v.MonthlyExcuses)
        {
            if (excuse.TimeFrom.HasValue && excuse.TimeTo.HasValue)
            {
                var duration = (float)(excuse.TimeTo.Value - excuse.TimeFrom.Value).TotalHours;
                _v.TotalExcuseDuration += duration;
            }
        }
        
        return View(_v);
    }
    
    [HttpGet("AttendanceReport")]
    public IActionResult AttendanceReport(DateTime? from, DateTime? to, int Dg = 0, int Dept = -1, int Section = 0, int status = 0)
    {
        // Set default date filters if not provided
        from ??= DateTime.Today.AddDays(-30); // Default to last 30 days
        to ??= DateTime.Today;
        
        // Store filter values in viewmodel
        _v.FromDate = from.Value;
        _v.ToDate = to.Value;
        _v.SelectedDg = Dg;
        _v.SelectedDept = Dept;
        _v.SelectedSection = Section;
        _v.StatusFilter = (AttendenceViewModel.AttendanceStatus)status;
        
        // Get all employees based on organizational filters
        var employeeQuery = _db.VempDtls.Where(v => v.DesgnHierLevel > 4 && !new List<int> { 2, 3, 4, 12, 13 }.Contains(v.DesgType.Value));
        
        // Apply organizational filters
        if (Dg > 0)
        {
            employeeQuery = employeeQuery.Where(e => e.DgCode == Dg);
        }
        
        if (Dept > -1)
        {
            employeeQuery = employeeQuery.Where(e => e.DeptCode == Dept);
        }

        if (Section > 0)
        {
            employeeQuery = employeeQuery.Where(e => e.SectionCode == Section);
        }
        
        // The SectionCode filter needs special handling since VempDtl doesn't have it
        List<int> employeeNumbers = new List<int>();
        employeeNumbers = employeeQuery.Where(e => e.EmpNo.HasValue).Select(e => e.EmpNo.Value).ToList();
        
        // Get filtered employees
        var employees = employeeQuery.ToList();
        _v.VempDtls = employees;
        
        // Get all DGs, Departments, and Sections for filter dropdowns
        _v.Dgs = _db.TdgCodes.Where(d => d.DgCode > 0  ).OrderBy(d => d.DgDespA).ToList();
        _v.Departments = _db.TDeptCode.Where(d => d.DeptCode > -1).OrderBy(d => d.DeptDespA).ToList();
        _v.Sections = _db.TsectionCodes.Where(s => s.SectionCode > 0).OrderBy(s => s.SectionDespA).ToList();
        
        // Get attendance records for these employees in the date range
        _v.DailyAttendance = _db.EmpWorkHours
            .Where(w => employeeNumbers.Contains(w.EmpNo) && 
                  w.Day.Date >= from.Value.Date && 
                  w.Day.Date <= to.Value.Date)
            .OrderBy(w => w.Day)
            .ThenBy(w => w.EmpNo)
            .ToList();
            
        // Get previously sent warnings for these employees in the date range
        _v.AttendanceWarnings = _db.AttendanceWarnings
            .Where(w => employeeNumbers.Contains(w.EmpNo) && 
                  w.AttendanceDate.Date >= from.Value.Date && 
                  w.AttendanceDate.Date <= to.Value.Date)
            .ToList();
        

    
        
        // Get leave applications for these employees in the date range
        _v.AllLeaveApplications = _db.TleaveAplTxs
            .Where(l => employeeNumbers.Contains(l.EmpNo) && 
                  l.CancelFlag != 1 && // Not canceled
                  l.ReqStat != 3 && // not rejected
                  (l.LeaveStartDate >= from.Value.Date && l.LeaveStartDate <= to.Value.Date || 
                   l.LeaveEndDate >= from.Value.Date && l.LeaveEndDate <= to.Value.Date ||
                   l.LeaveStartDate <= from.Value.Date && l.LeaveEndDate >= to.Value.Date))
            .Include(l => l.TleaveCode) // Include the leave code description
            .ToList();

        var tempTrgHist = _db.TempTrgHist
            .Where(t =>  employeeNumbers.Contains(t.EmpNo) && 
                  t.CourseStartDate >= from.Value.Date && t.CourseEndDate <= to.Value.Date &&
                  (t.CourseStartDate >= from.Value.Date && t.CourseStartDate <= to.Value.Date || 
                   t.CourseEndDate >= from.Value.Date && t.CourseEndDate <= to.Value.Date ||
                   t.CourseStartDate <= from.Value.Date && t.CourseEndDate >= to.Value.Date))
            .OrderBy(t => t.CourseStartDate)
            .ToList();

        _v.AllTraining = tempTrgHist;
        
        // Get excuse requests for these employees in the date range
        _v.AllExcuses = _db.Absents
            .Where(e => e.EmpNo.HasValue && employeeNumbers.Contains(e.EmpNo.Value) && 
                  e.OrderDate.HasValue && e.OrderDate.Value.Date >= from.Value.Date && e.OrderDate.Value.Date <= to.Value.Date)
            .OrderBy(e => e.OrderDate)
            .ToList();
            
        // Get holidays in the date range
        var holidays = _db.Tholidays
            .Where(h => h.Holiday.HasValue && 
                   h.Holiday.Value.Date >= from.Value.Date && 
                   h.Holiday.Value.Date <= to.Value.Date)
            .Select(h => h.Holiday.Value.Date)
            .ToList();
        
        _v.Holidays = holidays;
        
        // Calculate statistics
        _v.Statistics = new Dictionary<string, int>();
        
        // Count total work days in the period (excluding weekends and holidays)
        int totalWorkDays = 0;
        for (var date = from.Value.Date; date <= to.Value.Date; date = date.AddDays(1))
        {
            if (date.DayOfWeek != DayOfWeek.Friday && 
                date.DayOfWeek != DayOfWeek.Saturday && 
                !holidays.Contains(date.Date))
            {
                totalWorkDays++;
            }
        }
        _v.Statistics["TotalWorkDays"] = totalWorkDays;
        
        // Prepare statistics by status
        _v.Statistics["CompleteAttendance"] = 0;
        _v.Statistics["IncompleteAttendance"] = 0;
        _v.Statistics["Absent"] = 0;
        _v.Statistics["Excused"] = 0;
        _v.Statistics["Training"] = 0;
        _v.Statistics["Leave"] = 0;
        // Count records by status (for each employee and each workday)
        foreach (var emp in employees)
        {
            if (!emp.EmpNo.HasValue) continue;
            
            for (var date = from.Value.Date; date <= to.Value.Date; date = date.AddDays(1))
            {
                if (_v.IsWeekend(date) || _v.IsHoliday(date)) continue;
                
                // Skip if employee is on leave
                if (_v.GetAllLeaveForDate(emp.EmpNo.Value, date) != null) {
                    _v.Statistics["Leave"]++;
                    continue;
                }

                if (_v.GetAllTrainingForDate(emp.EmpNo.Value, date) != null) {
                    _v.Statistics["Training"]++;
                    continue;
                }
                
                var attendanceRecord = _v.DailyAttendance.FirstOrDefault(a => a.EmpNo == emp.EmpNo && a.Day.Date == date.Date);
                var attendanceStatus = _v.GetAttendanceStatus(emp.EmpNo.Value, date, attendanceRecord);
                
                // Update counter for the appropriate status
                switch (attendanceStatus)
                {
                    case AttendenceViewModel.AttendanceStatus.Complete:
                        _v.Statistics["CompleteAttendance"]++;
                        break;
                    case AttendenceViewModel.AttendanceStatus.Incomplete:
                        _v.Statistics["IncompleteAttendance"]++;
                        break;
                    case AttendenceViewModel.AttendanceStatus.Absent:
                        _v.Statistics["Absent"]++;
                        break;
           
                }
            }
        }
        
        // Count employees with attendance records
        var uniqueAttendanceDays = _v.DailyAttendance
            .GroupBy(a => new { a.EmpNo, a.Day.Date })
            .Select(g => g.Key)
            .ToList();
        
        _v.Statistics["TotalAttendanceRecords"] = uniqueAttendanceDays.Count;
        
        // Count employees with leave
        var uniqueLeaveEmployees = _v.AllLeaveApplications
            .Select(l => l.EmpNo)
            .Distinct()
            .Count();
        
        _v.Statistics["EmployeesOnLeave"] = uniqueLeaveEmployees;
        
        // Count employees with excuses
        var uniqueExcuseEmployees = _v.AllExcuses
            .Where(e => e.EmpNo.HasValue)
            .Select(e => e.EmpNo.Value)
            .Distinct()
            .Count();
        
        _v.Statistics["EmployeesWithExcuses"] = uniqueExcuseEmployees;
        
        // Expected attendance (employees × work days)
        _v.Statistics["ExpectedAttendance"] = employees.Count * totalWorkDays;
        
        // Attendance rate (actual/expected)
        if (_v.Statistics["ExpectedAttendance"] > 0)
        {
            double attendancePercentage = (double)_v.Statistics["TotalAttendanceRecords"] / _v.Statistics["ExpectedAttendance"] * 100;
            _v.AttendancePercentage = Math.Round(attendancePercentage, 2);
        }
        else
        {
            _v.AttendancePercentage = 0;
        }

    
        
        return View(_v);
    }
    
    [HttpPost("SendWarningEmail")]
    public async Task<IActionResult> SendWarningEmail(int warningEmpNo, string warningDate, string warningMessage)
    {
        try
        {
            // Get employee information
            var employee = _db.VempDtls.FirstOrDefault(e => e.EmpNo == warningEmpNo);
            if (employee == null)
            {
                return Json(new { success = false, message = "Employee not found" });
            }
            
            string emailAddress = employee.EmailId + "@gsc.local";

            // Generate email address
            //string emailAddress = $"{warningEmpNo}@mail.gsc.local";
            
            // Parse date for formatting
            DateTime parsedDate;
            if (!DateTime.TryParse(warningDate, out parsedDate))
            {
                parsedDate = DateTime.Now;
            }
            
            // Create variables dictionary for email template
            var variables = new Dictionary<string, string>
            {
                { "EmpNameA", employee.EmpNameA },
                { "EmpNo", employee.EmpNo.ToString() },
                { "Date", parsedDate.ToString("yyyy-MM-dd") },
                { "DgDespA", employee.DgDespA ?? "المديرية" },
                { "DeptDespA", employee.DeptDespA ?? "القسم" },
            };
            

            var message = _emailService.BindVariables(warningMessage, variables);

            // Use template to send email - the EmailService will automatically look for the template file
            await _emailService.SendEmailAsync(
                emailAddress,
                "تنبيه بشأن الحضور والانصراف",
                message
            );
            
            // Record that a warning has been sent to this employee for this date
            var warning = new AttendanceWarning
            {
                EmpNo = warningEmpNo,
                AttendanceDate = parsedDate
            };
            
            // Check if a warning already exists for this employee and date
            var existingWarning = _db.AttendanceWarnings
                .FirstOrDefault(w => w.EmpNo == warningEmpNo && w.AttendanceDate.Date == parsedDate.Date);

            if (existingWarning == null)
            {
                // Add new warning record
                _db.AttendanceWarnings.Add(warning);
                await _db.SaveChangesAsync();

                var employeeWarning = new EmployeeWarning
                {
                    EmpNo = warningEmpNo,
                    Title = "تنبيه بشأن الحضور والانصراف",
                    Body = warningMessage,
                    WarningDate = parsedDate
                };
                _db.EmployeeWarnings.Add(employeeWarning);
                await _db.SaveChangesAsync();

            }
            
            // Log the email sending
            _logger.LogInformation($"Warning email sent to {emailAddress} regarding attendance on {warningDate}");
            
            return Json(new { 
                success = true,
                message = new List<string> { $"تم إرسال التنبيه إلى {emailAddress} بنجاح" },
                action="$('#warning-button-" + warningEmpNo + "-" + warningDate + "').html('<span class=\"badge bg-success\"><i class=\"fas fa-check\"></i> تم إرسال التنبيه</span>'); $('#modal-warning').modal('hide');" });
        }
        catch (Exception ex)
        {
            // Log the error
            _logger.LogError(ex, $"Error sending warning email: {ex.Message}");
            return Json(new { success = false, message = new List<string> { $"خطأ في إرسال البريد الإلكتروني: {ex.Message}" } });
        }
    }
}
