﻿using System;
using System.Collections.Generic;
using System.Linq;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Attendence.Models.Entities;
using HumanResource.Modules.Execuses.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.ViewModels;

namespace HumanResource.Modules.Attendence.ViewModels;

// Enum to represent attendance status for filtering

public class AttendenceViewModel : BaseViewModel
{
    hrmsContext _db;

    public AttendenceViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
        _db = context;
    }

    public enum AttendanceStatus
    {
        // All = 0, 
        Complete = 1,     // دوام كامل
        Incomplete = 2,   // دوام ناقص
        Absent = 3,       // غائب

    }


    public List<VempDtl> VempDtls { get; set; }
    public List<VempDtl> Managers { get; set; }
    public VempDtl VempDtl { get; set; }
    public VempDtl Employee { get; set; }
    public TEmpWorkShift Shift { get; set; }
    public List<TEmpWorkShift> Shifts { get; set; }
    
    // Properties for attendance reports
    public DateTime SelectedDate { get; set; }
    public int SelectedMonth { get; set; }
    public int SelectedYear { get; set; }
    public List<EmpWorkHours> DailyAttendance { get; set; }
    public Dictionary<DateTime, List<int>> AttendanceByDate { get; set; }
    
    // Properties for My Attendance view
    public List<Absent> MonthlyExcuses { get; set; }
    public float TotalExcuseDuration { get; set; }
    
    // Properties for leave information
    public List<TleaveAplTx> LeaveApplications { get; set; }
    public List<TleaveCode> LeaveCodes { get; set; }
    
    // Properties for comprehensive attendance report
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int SelectedDg { get; set; }
    public int SelectedDept { get; set; }
    public int SelectedSection { get; set; }
    public AttendanceStatus StatusFilter { get; set; } = AttendanceStatus.Absent;
    public List<TdgCode> Dgs { get; set; }
    public List<TDeptCode> Departments { get; set; }
    public List<TsectionCode> Sections { get; set; }
    public List<TleaveAplTx> AllLeaveApplications { get; set; }
    public List<Absent> AllExcuses { get; set; }
    public Dictionary<string, int> Statistics { get; set; }
    public double AttendancePercentage { get; set; }
    public List<DateTime> Holidays { get; set; } = new List<DateTime>();

    public List<TempTrgHist> AllTraining { get; set; }
    
    // List of attendance warnings that have been sent
    public List<AttendanceWarning> AttendanceWarnings { get; set; }
    
    // Helper method to calculate standard work hours (7 hours is 100%)
    public int CalculateWorkPercentage(float hours)
    {
        // 7 hours is considered 100%
        int percentage = (int)Math.Round(hours / 7 * 100);
        
        // Cap at 100%
        return Math.Min(percentage, 100);
    }
    
    // Helper method to get progress bar color class based on work hours
    public string GetProgressColorClass(float hours)
    {
        if (hours < 5.5) // Less than 78.6%
            return "bg-danger";
        else if (hours < 6.5) // Less than 92.9%
            return "bg-warning";
        else
            return "bg-success";
    }
    
    // Helper method to get proper formatting for TimeSpan
    public string FormatExcuseDuration(float hours)
    {
        int totalMinutes = (int)(hours * 60);
        int displayHours = totalMinutes / 60;
        int displayMinutes = totalMinutes % 60;
        
        return $"{displayHours}:{displayMinutes:D2}";
    }
    
    // Helper method to check if a date is a weekend (Friday or Saturday)
    public bool IsWeekend(DateTime date)
    {
        return date.DayOfWeek == DayOfWeek.Friday || date.DayOfWeek == DayOfWeek.Saturday;
    }
    
    // Helper method to check if a date is a holiday
    public bool IsHoliday(DateTime date)
    {
        return Holidays?.Contains(date.Date) ?? false;
    }
    
    // Helper method to check if a date is a workday
    public bool IsWorkday(DateTime date)
    {
        return !IsWeekend(date) && !IsHoliday(date);
    }
    
    // Helper method to check if an employee is on leave for a specific date
    public TleaveAplTx GetLeaveForDate(int employeeNo, DateTime date)
    {
        if (LeaveApplications == null)
            return null;
            
        return LeaveApplications.FirstOrDefault(l => 
            l.EmpNo == employeeNo && 
            date.Date >= l.LeaveStartDate.Date && 
            date.Date <= l.LeaveEndDate.Date &&
            l.CancelFlag == 0 && // Not canceled
            (l.ReqStat == 1 || l.ReqStat == 4 || l.ReqStat == 6)); // Approved, Generated, or Finalized
    }
    
    // Helper method to get leave description by code
    public string GetLeaveDescription(int leaveCode)
    {
        if (LeaveCodes == null)
            return "إجازة";
            
        var leaveType = LeaveCodes.FirstOrDefault(c => c.LeaveCode == leaveCode);
        return leaveType?.LeaveDespA ?? "إجازة";
    }
    
    // Helper method to check if an employee has an excuse record for a specific date
    public Absent GetExcuseForDate(int employeeNo, DateTime date)
    {
        if (AllExcuses == null)
            return null;
            
        return AllExcuses.FirstOrDefault(e => 
            e.EmpNo == employeeNo && 
            e.OrderDate.HasValue && e.OrderDate.Value.Date == date.Date);
    }
    
    // Helper method to calculate excuse duration in hours
    public float CalculateExcuseDuration(Absent excuse)
    {
        if (excuse == null || !excuse.TimeFrom.HasValue || !excuse.TimeTo.HasValue)
            return 0;
            
        return (float)(excuse.TimeTo.Value - excuse.TimeFrom.Value).TotalHours;
    }
    
    // Helper method to determine attendance status
    public AttendanceStatus GetAttendanceStatus(int employeeNo, DateTime date, EmpWorkHours attendanceRecord)
    {
        // Check if employee has an excuse for the date
        var excuseRecord = GetExcuseForDate(employeeNo, date);
        if (excuseRecord != null)
            return AttendanceStatus.Incomplete;
            
        // No attendance record means the employee is absent
        if (attendanceRecord == null)
            return AttendanceStatus.Absent;
            
        // Determine if attendance is complete or incomplete based on hours
        return attendanceRecord.Hours < 6 ? AttendanceStatus.Incomplete : AttendanceStatus.Complete;
    }
    
    // Helper method to check if an employee is on leave for a specific date (for the comprehensive report)
    public TleaveAplTx GetAllLeaveForDate(int employeeNo, DateTime date)
    {
        if (AllLeaveApplications == null)
            return null;
            
        return AllLeaveApplications.FirstOrDefault(l => 
            l.EmpNo == employeeNo && 
            date.Date >= l.LeaveStartDate.Date && 
            date.Date <= l.LeaveEndDate.Date &&
            l.CancelFlag == 0 && // Not canceled
            (l.ReqStat == 1 || l.ReqStat == 4 || l.ReqStat == 6)); // Approved, Generated, or Finalized
    }

    public TempTrgHist GetAllTrainingForDate(int employeeNo, DateTime date)
    {
        if (AllTraining == null)
            return null;
            
        return AllTraining.FirstOrDefault(t => 
            t.EmpNo == employeeNo && 
            date.Date >= t.CourseStartDate.Date && 
            date.Date <= t.CourseEndDate.Date);
    }

    // Helper method to generate employee email
    public string GetEmployeeEmail(int employeeNo)
    {
        return $"{employeeNo}@mail.gsc.local";
    }
    
    // Helper method to check if a warning has been sent for an employee on a specific date
    public bool HasWarningSent(int employeeNo, DateTime date)
    {
        if (AttendanceWarnings == null)
            return false;
            
        return AttendanceWarnings.Any(w => 
            w.EmpNo == employeeNo && 
            w.AttendanceDate.Date == date.Date);
    }
}

