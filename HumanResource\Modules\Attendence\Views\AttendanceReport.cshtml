@model AttendenceViewModel
@using HumanResource.Modules.Shared.ViewModels
@using HumanResource.Modules.Attendence.ViewModels


<h3 class="float-end">@ViewData["Title"]</h3>
    <div class="card mb-4">
   
        <div class="card-body">
            <form method="get">
                <div class="row mb-3">
                    <!-- Date Range Filters -->
                    <div class="col-md-2">
                        <label for="from" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="from" name="from" value="@Model.FromDate.ToString("yyyy-MM-dd")">
                    </div>
                    <div class="col-md-2">
                        <label for="to" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="to" name="to" value="@Model.ToDate.ToString("yyyy-MM-dd")">
                    </div>
                    
                    <!-- Organizational Filters -->
                    <div class="col-md-2">
                        <label for="Dg" class="form-label"> المديرية</label>
                        <select class="form-control" id="Dg" name="Dg" onchange="updateDepartments()">
                            <option value="0">-- الكل --</option>
                            @foreach (var dg in Model.Dgs.OrderBy(d => d.DgDespA))
                            {
                                if (Model.SelectedDg == dg.DgCode)
                                {
                                    <option value="@dg.DgCode" selected>@dg.DgDespA</option>
                                }
                                else
                                {
                                    <option value="@dg.DgCode">@dg.DgDespA</option>
                                }
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="Dept" class="form-label">الدائرة</label>
                        <select class="form-control" id="Dept" name="Dept" onchange="updateSections()">
                            <option value="-1">-- الكل --</option>
                            @foreach (var dept in Model.Departments.Where(d => Model.SelectedDg == 0 || d.DgCode == Model.SelectedDg).OrderBy(d => d.DeptDespA))
                            {
                                if (Model.SelectedDept == dept.DeptCode)
                                {
                                    <option value="@dept.DeptCode" selected>@dept.DeptDespA</option>
                                }
                                else
                                {
                                    <option value="@dept.DeptCode">@dept.DeptDespA</option>
                                }
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="Section" class="form-label">القسم</label>
                        <select class="form-control" id="Section" name="Section">
                            <option value="0">-- الكل --</option>
                            @foreach (var sec in Model.Sections.Where(s => 
                                (Model.SelectedDept == 0 || s.DeptCode == Model.SelectedDept) && 
                                (Model.SelectedDg == 0 || s.DgCode == Model.SelectedDg)).OrderBy(s => s.SectionDespA))
                            {
                                if (Model.SelectedSection == sec.SectionCode)
                                {
                                    <option value="@sec.SectionCode" selected>@sec.SectionDespA</option>
                                }
                                else
                                {
                                    <option value="@sec.SectionCode">@sec.SectionDespA</option>
                                }
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-control" id="status" name="status">
                            
                            
                            @if (Model.StatusFilter == AttendenceViewModel.AttendanceStatus.Complete)
                            {
                                <option value="1" selected>دوام كامل</option>
                            }
                            else
                            {
                                <option value="1">دوام كامل</option>
                            }
                            
                            @if (Model.StatusFilter == AttendenceViewModel.AttendanceStatus.Incomplete)
                            {
                                <option value="2" selected>دوام ناقص</option>
                            }
                            else
                            {
                                <option value="2">دوام ناقص</option>
                            }
                            
                            @if (Model.StatusFilter == AttendenceViewModel.AttendanceStatus.Absent)
                            {
                                <option value="3" selected>غائب</option>
                            }
                            else
                            {
                                <option value="3">غائب</option>
                            }
                          
                        </select>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">تطبيق الفلتر</button>
                        <button type="button" class="btn btn-warning ms-2" onclick="showBulkWarningModal()">
                            <i class="fas fa-envelope-bulk"></i> إرسال تنبيهات جماعية
                        </button>
                        @if (Model._h.Can("admin"))
                        {
                            <button type="button" class="btn btn-info ms-2" onclick="testBulkWarningSetup()">
                                <i class="fas fa-cog"></i> اختبار النظام
                            </button>
                        }
                    </div>
                </div>
            </form>
            
            <!-- Statistics Summary -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2 text-center">
                                    <h6>أيام العمل</h6>
                                    <h4 class="text-primary">@Model.Statistics["TotalWorkDays"]</h4>
                                </div>
                                <div class="col-md-2 text-center">
                                    <h6>دوام كامل</h6>
                                    <h4 class="text-success">@Model.Statistics["CompleteAttendance"]</h4>
                                </div>
                                <div class="col-md-2 text-center">
                                    <h6>دوام ناقص</h6>
                                    <h4 class="text-warning">@Model.Statistics["IncompleteAttendance"]</h4>
                                </div>
                                <div class="col-md-2 text-center">
                                    <h6>غائب</h6>
                                    <h4 class="text-danger">@Model.Statistics["Absent"]</h4>
                                </div>
                                <div class="col-md-2 text-center">
                                    <h6>استئذان</h6>
                                    <h4 class="text-info">@Model.Statistics["Excused"]</h4>
                                </div>
                                <div class="col-md-2 text-center">
                                    <h6>نسبة الحضور</h6>
                                    @{
                                        string attendanceClass = Model.AttendancePercentage < 75 ? "text-danger" : 
                                            (Model.AttendancePercentage < 90 ? "text-warning" : "text-success");
                                    }
                                    <h4 class="@attendanceClass">
                                        @Model.AttendancePercentage.ToString("F1")%
                                    </h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Attendance Time Distribution Chart -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <h5 class="mb-0"><i class="fas fa-chart-bar mr-2"></i> تحليل توزيع أوقات الحضور والانصراف</h5>
                    <div class=" border-primary border-2 rounded rounded-3 p-3">
                        
                        <div class="">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container position-relative" style="height:300px;">
                                        <canvas id="entryTimeChart"></canvas>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container position-relative" style="height:300px;">
                                        <canvas id="exitTimeChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="alert bg-primary text-white">
                                        <h6><i class="fas fa-info-circle mr-2"></i>تحليل البيانات</h6>
                                        <div id="chartAnalysis"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
            
            <!-- Attendance Table -->
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="attendanceTable">
                    <thead class="bg-primary text-white">
                        <tr>
                            <th>الرقم الوظيفي</th>
                            <th>اسم الموظف</th>
                            <th>الإدارة</th>
                            <th>التاريخ</th>
                            <th>وقت الحضور</th>
                            <th>وقت الانصراف</th>
                            <th>ساعات العمل</th>
                            <th>الحالة</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var emp in Model.VempDtls.OrderBy(e => e.EmpNameA))
                        {
                            bool hasRecord = false;
                            
                            for (DateTime date = Model.FromDate.Date; date <= Model.ToDate.Date; date = date.AddDays(1))
                            {
                                if (Model.IsWeekend(date) || Model.IsHoliday(date))
                                {
                                    continue; // Skip weekends and holidays
                                }
                                
                                var leaveRecord = Model.GetAllLeaveForDate(emp.EmpNo.Value, date);
                                if (leaveRecord != null)
                                {
                             
                                        
                                    hasRecord = true;
                                   
                                    continue;
                                }

                                if (Model.GetAllTrainingForDate(emp.EmpNo.Value, date) != null)
                                {

                               

                                  
                                    continue;
                                
                                }
                                
                                var attendanceRecord = Model.DailyAttendance.FirstOrDefault(a => a.EmpNo == emp.EmpNo && a.Day.Date == date.Date);
                                var excuseRecord = Model.GetExcuseForDate(emp.EmpNo.Value, date);
                                var attendanceStatus = Model.GetAttendanceStatus(emp.EmpNo.Value, date, attendanceRecord);
                                
                                // Skip if filtering by status and record doesn't match
                                if ( 
                                    Model.StatusFilter != attendanceStatus)
                                    continue;
                                
                                if (attendanceRecord != null)
                                {
                                    hasRecord = true;
                                    <tr>
                                        <td>@emp.EmpNo</td>
                                        <td>@emp.EmpNameA</td>
                                        <td>@emp.DeptDespA</td>
                                        <td>@date.ToString("yyyy-MM-dd")</td>
                                        <td>
                                            @(attendanceRecord.FirstEntry.ToString("HH:mm"))
                                        </td>
                                        <td>
                                            @(attendanceRecord.LastEntry.ToString("HH:mm"))
                                        </td>
                                        <td>
                                            @if (attendanceRecord.Hours > 0)
                                            {
                                                var percentage = Model.CalculateWorkPercentage(attendanceRecord.Hours);
                                                <div class="progress rounded-pill shadow-sm" style="height: 20px;">
                                                    <div class="progress-bar rounded-pill @Model.GetProgressColorClass(attendanceRecord.Hours)" 
                                                         role="progressbar"
                                                         style="width: @(percentage)%;" 
                                                         aria-valuenow="@percentage" 
                                                         aria-valuemin="0" 
                                                         aria-valuemax="100">
                                                        @Model._h.FormatHour(attendanceRecord.Hours)
                                                    </div>
                                                </div>
                                            }
                                            else
                                            {
                                                <span>-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (excuseRecord != null)
                                            {
                                                float excuseDuration = Model.CalculateExcuseDuration(excuseRecord);
                                                <span class="badge bg-warning">استئذان</span>
                                                <small class="d-block mt-1">
                                                    @excuseRecord.TimeFrom?.ToString("HH:mm") - @excuseRecord.TimeTo?.ToString("HH:mm")
                                                    (@excuseDuration.ToString("F1") ساعة)
                                                </small>
                                                <small class="d-block mt-1 text-muted">@(excuseRecord.Reson ?? "")</small>
                                            }
                                            else if (attendanceRecord.Hours < 6)
                                            {
                                                <span class="badge bg-danger">دوام ناقص</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">دوام كامل</span>
                                            }
                                        </td>
                                        <td>
                                            @if (attendanceRecord.Hours < 6)
                                            {
                                                @if (Model.HasWarningSent(emp.EmpNo.Value, date))
                                                {
                                                    <span class="badge bg-success"><i class="fas fa-check"></i> تم إرسال التنبيه</span>
                                                }
                                                else
                                                {
                                                    <button type="button" class="btn btn-sm btn-light" 
                                                            onclick="sendWarningEmail(@emp.EmpNo, '@emp.EmpNameA', '@date.ToString("yyyy-MM-dd")')">
                                                        <i class="fas fa-envelope"></i> إرسال تنبيه
                                                    </button>
                                                }
                                            }
                                        </td>
                                    </tr>
                                }
                                else
                                {
                                    // No attendance record and no leave record - this is absence
                                    hasRecord = true;
                                    <tr class="bg-danger bg-opacity-10">
                                        <td>@emp.EmpNo</td>
                                        <td>@emp.EmpNameA</td>
                                        <td>@emp.DeptDespA</td>
                                        <td>@date.ToString("yyyy-MM-dd")</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>
                                            <span class="badge bg-danger">غائب</span>
                                        </td>
                                        <td id="<EMAIL><EMAIL>("yyyy-MM-dd")">
                                            @if (Model.HasWarningSent(emp.EmpNo.Value, date))
                                            {
                                                <span class="badge bg-success"><i class="fas fa-check"></i> تم إرسال التنبيه</span>
                                            }
                                            else
                                            {
                                                <button type="button" class="btn btn-sm btn-light" 
                                                        onclick="sendWarningEmail(@emp.EmpNo, '@emp.EmpNameA', '@date.ToString("yyyy-MM-dd")')">
                                                    <i class="fas fa-envelope"></i> إرسال تنبيه
                                                </button>
                                            }
                                        </td>
                                    </tr>
                                }
                            }
                            
                            @if (!hasRecord)
                            {
                                // If employee has no records in the selected period at all
                                @* <tr class="bg-secondary bg-opacity-10">
                                    <td>@emp.EmpNo</td>
                                    <td>@emp.EmpNameA</td>
                                    <td>@emp.DeptDespA</td>
                                    <td colspan="5">لا توجد بيانات في الفترة المحددة</td>
                                    <td></td>
                                </tr> *@

                                
                            }
                        }
                    </tbody>
                </table>
            </div>
        
    </div>


<form action="@Url.Action("SendWarningEmail", "Attendence")" method="post" class="ajax">

    <!-- Email Warning Modal -->
<div class="modal fade" id="warningEmailModal" tabindex="-1" aria-labelledby="warningEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="warningEmailModalLabel">إرسال تنبيه</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="warningEmailForm">
                    <input type="hidden" id="warningEmpNo" name="warningEmpNo">
                    <input type="hidden" id="warningDate" name="warningDate">
                    
                    <div class="mb-3">
                        <label for="warningEmployeeName" class="form-label">اسم الموظف</label>
                        <input type="text" class="form-control" id="warningEmployeeName" readonly>
                    </div>


                    <div class="mb-3">
                        <label for="warningTemplate" class="form-label">نوع التنبيه</label>
                        <select class="form-control" id="warningTemplate" name="warningTemplate" onchange="loadWarningTemplate(this.value)">
                            <option value="attendence.warning1">تنبيه بشأن الحضور والانصراف</option>
                            <option value="attendence.warning2">تنبيه بشأن عدم الحضور</option>
                        </select>
                    </div>
                    

                    <div class="mb-3">
                        <label for="warningMessage" class="form-label">رسالة التنبيه</label>
                        <textarea class="form-control" id="warningMessage" name="warningMessage" rows="5">@Html.Raw(Model._h.Settings.Get("email.template.attendence.warning1", ""))</textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="submit" class="btn btn-warning" >إرسال التنبيه</button>
            </div>
        </div>
    </div>
</div>
</form>

<!-- Bulk Warning Modal -->
<div class="modal fade" id="bulkWarningModal" tabindex="-1" aria-labelledby="bulkWarningModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="bulkWarningModalLabel">إرسال تنبيهات جماعية</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulkWarningForm">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        سيتم إرسال التنبيهات للموظفين بناءً على الفلاتر المحددة حالياً والحالة المختارة أدناه.
                        العملية ستتم في الخلفية ويمكنك متابعة التقدم.
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="bulkStatusFilter" class="form-label">حالة الحضور للتنبيه</label>
                            <select class="form-control" id="bulkStatusFilter" name="bulkStatusFilter">
                                <option value="3">غائب</option>
                                <option value="2">دوام ناقص</option>
                                <option value="1">دوام كامل</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="bulkWarningTemplate" class="form-label">نوع التنبيه</label>
                            <select class="form-control" id="bulkWarningTemplate" name="bulkWarningTemplate" onchange="loadBulkWarningTemplate(this.value)">
                                <option value="attendence.warning1">تنبيه بشأن الحضور والانصراف</option>
                                <option value="attendence.warning2">تنبيه بشأن عدم الحضور</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="bulkWarningMessage" class="form-label">رسالة التنبيه</label>
                        <textarea class="form-control" id="bulkWarningMessage" name="bulkWarningMessage" rows="5">@Html.Raw(Model._h.Settings.Get("email.template.attendence.warning1", ""))</textarea>
                        <small class="form-text text-muted">
                            يمكن استخدام المتغيرات التالية: {EmpNameA}, {EmpNo}, {Date}, {DgDespA}, {DeptDespA}, {Reason}
                        </small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmBulkSend" required>
                            <label class="form-check-label" for="confirmBulkSend">
                                أؤكد أنني أريد إرسال التنبيهات الجماعية للموظفين المحددين
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="startBulkWarning()">
                    <i class="fas fa-paper-plane"></i> بدء الإرسال
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Warning Progress Modal -->
<div class="modal fade" id="bulkWarningProgressModal" tabindex="-1" aria-labelledby="bulkWarningProgressModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="bulkWarningProgressModalLabel">تقدم إرسال التنبيهات</h5>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">حالة العملية:</label>
                    <span id="bulkJobStatus" class="badge bg-info">جاري التحضير...</span>
                </div>

                <div class="mb-3">
                    <label class="form-label">التقدم:</label>
                    <div class="progress mb-2">
                        <div id="bulkProgressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            0%
                        </div>
                    </div>
                    <small class="text-muted">
                        <span id="bulkProgressText">0 من 0 موظف</span>
                    </small>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">إجمالي الموظفين</h6>
                                <h4 id="bulkTotalEmployees" class="text-primary">0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">تم الإرسال بنجاح</h6>
                                <h4 id="bulkSuccessfulEmails" class="text-success">0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">فشل الإرسال</h6>
                                <h4 id="bulkFailedEmails" class="text-danger">0</h4>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="bulkErrorsSection" class="mt-3" style="display: none;">
                    <label class="form-label">الأخطاء:</label>
                    <div id="bulkErrorsList" class="alert alert-danger">
                    </div>
                </div>

                <div id="bulkCompletedSection" class="mt-3" style="display: none;">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        تم إكمال عملية إرسال التنبيهات بنجاح!
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="bulkProgressCloseBtn" disabled>إغلاق</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/assets/js/chart.js"></script>
    <script>
        $(document).ready(function() {
            createDatatable('#attendanceTable');
            createAttendanceCharts();
        });
        
        function createAttendanceCharts() {
            try {
                // Extract and process attendance data
                var attendanceData = @Html.Raw(Json.Serialize(Model.DailyAttendance));
                
                // Time ranges for entry
                var before7AM = 0;
                var between7and8AM = 0;
                var between8and9AM = 0;
                var after9AM = 0;
                
                // Time ranges for exit
                var before2PM = 0;
                var between2and3PM = 0;
                var between3and4PM = 0;
                var after4PM = 0;
                
                // Process attendance data
                attendanceData.forEach(function(record) {
                    try {
                        // Process entry time
                        var entryDate = new Date(record.firstEntry);
                        if (!isNaN(entryDate.getTime())) {
                            var entryHour = entryDate.getHours();
                            var entryMinutes = entryDate.getMinutes();
                            var entryTimeDecimal = entryHour + (entryMinutes / 60);
                            
                            if (entryTimeDecimal < 7) {
                                before7AM++;
                            } else if (entryTimeDecimal >= 7 && entryTimeDecimal < 8) {
                                between7and8AM++;
                            } else if (entryTimeDecimal >= 8 && entryTimeDecimal < 9) {
                                between8and9AM++;
                            } else {
                                after9AM++;
                            }
                        }
                        
                        // Process exit time
                        var exitDate = new Date(record.lastEntry);
                        if (!isNaN(exitDate.getTime())) {
                            var exitHour = exitDate.getHours();
                            var exitMinutes = exitDate.getMinutes();
                            var exitTimeDecimal = exitHour + (exitMinutes / 60);
                            
                            if (exitTimeDecimal < 14) { // Before 2 PM
                                before2PM++;
                            } else if (exitTimeDecimal >= 14 && exitTimeDecimal < 15) { // Between 2-3 PM
                                between2and3PM++;
                            } else if (exitTimeDecimal >= 15 && exitTimeDecimal < 16) { // Between 3-4 PM
                                between3and4PM++;
                            } else { // After 4 PM
                                after4PM++;
                            }
                        }
                    } catch (err) {
                        console.error("Error processing attendance record:", err);
                    }
                });
                
                // Display summary in console for debugging
                console.log("Chart Data Summary:");
                console.log("Entry times - Before 7AM:", before7AM, "7-8AM:", between7and8AM, "8-9AM:", between8and9AM, "After 9AM:", after9AM);
                console.log("Exit times - Before 2PM:", before2PM, "2-3PM:", between2and3PM, "3-4PM:", between3and4PM, "After 4PM:", after4PM);
                
                // Generate analysis text
                var totalEntries = before7AM + between7and8AM + between8and9AM + after9AM;
                var totalExits = before2PM + between2and3PM + between3and4PM + after4PM;
                
                var analysisHtml = '';
                
                if (totalEntries > 0) {
                    var earlyEntryPercent = Math.round((before7AM / totalEntries) * 100);
                    var lateEntryPercent = Math.round((after9AM / totalEntries) * 100);
                    var normalEntryPercent = Math.round(((between7and8AM + between8and9AM) / totalEntries) * 100);
                    
                    analysisHtml += '<p><strong>تحليل أوقات الحضور:</strong> ';
                    analysisHtml += earlyEntryPercent + '% من الموظفين يحضرون قبل الساعة 7 صباحاً، و';
                    analysisHtml += normalEntryPercent + '% يحضرون بين الساعة 7 و9 صباحاً، و';
                    analysisHtml += lateEntryPercent + '% يحضرون بعد الساعة 9 صباحاً.</p>';
                }
                
                if (totalExits > 0) {
                    var earlyExitPercent = Math.round((before2PM / totalExits) * 100);
                    var normalExitPercent = Math.round((between2and3PM / totalExits) * 100);
                    var lateExitPercent = Math.round(((between3and4PM + after4PM) / totalExits) * 100);
                    
                    analysisHtml += '<p><strong>تحليل أوقات الانصراف:</strong> ';
                    analysisHtml += earlyExitPercent + '% من الموظفين ينصرفون قبل الساعة 2 ظهراً، و';
                    analysisHtml += normalExitPercent + '% ينصرفون بين الساعة 2 و3 ظهراً، و';
                    analysisHtml += lateExitPercent + '% ينصرفون بعد الساعة 3 عصراً.</p>';
                }
                
                // Additional insights
                if (totalEntries > 0 && totalExits > 0) {
                    var mostCommonEntryTime = '';
                    var mostCommonEntryCount = 0;
                    var mostCommonExitTime = '';
                    var mostCommonExitCount = 0;
                    
                    if (before7AM > mostCommonEntryCount) {
                        mostCommonEntryTime = 'قبل 7 صباحاً';
                        mostCommonEntryCount = before7AM;
                    }
                    if (between7and8AM > mostCommonEntryCount) {
                        mostCommonEntryTime = 'بين 7-8 صباحاً';
                        mostCommonEntryCount = between7and8AM;
                    }
                    if (between8and9AM > mostCommonEntryCount) {
                        mostCommonEntryTime = 'بين 8-9 صباحاً';
                        mostCommonEntryCount = between8and9AM;
                    }
                    if (after9AM > mostCommonEntryCount) {
                        mostCommonEntryTime = 'بعد 9 صباحاً';
                        mostCommonEntryCount = after9AM;
                    }
                    
                    if (before2PM > mostCommonExitCount) {
                        mostCommonExitTime = 'قبل 2 ظهراً';
                        mostCommonExitCount = before2PM;
                    }
                    if (between2and3PM > mostCommonExitCount) {
                        mostCommonExitTime = 'بين 2-3 ظهراً';
                        mostCommonExitCount = between2and3PM;
                    }
                    if (between3and4PM > mostCommonExitCount) {
                        mostCommonExitTime = 'بين 3-4 عصراً';
                        mostCommonExitCount = between3and4PM;
                    }
                    if (after4PM > mostCommonExitCount) {
                        mostCommonExitTime = 'بعد 4 عصراً';
                        mostCommonExitCount = after4PM;
                    }
                    
                    analysisHtml += '<p><strong>ملخص:</strong> ';
                    analysisHtml += 'وقت الحضور الأكثر شيوعاً هو ' + mostCommonEntryTime + '، ';
                    analysisHtml += 'ووقت الانصراف الأكثر شيوعاً هو ' + mostCommonExitTime + '.</p>';
                }
                
                // Display analysis
                document.getElementById('chartAnalysis').innerHTML = analysisHtml || 'لا توجد بيانات كافية للتحليل.';
                
                // Create Entry Time Chart
                if (document.getElementById('entryTimeChart')) {
                    var entryCtx = document.getElementById('entryTimeChart').getContext('2d');
                    var entryTimeChart = new Chart(entryCtx, {
                        type: 'bar',
                        data: {
                            labels: ['قبل 7 صباحاً', 'بين 7-8 صباحاً', 'بين 8-9 صباحاً', 'بعد 9 صباحاً'],
                            datasets: [{
                                label: 'عدد الموظفين',
                                data: [before7AM, between7and8AM, between8and9AM, after9AM],
                                backgroundColor: [
                                    'rgba(75, 192, 192, 0.6)',
                                    'rgba(54, 162, 235, 0.6)',
                                    'rgba(255, 206, 86, 0.6)',
                                    'rgba(255, 99, 132, 0.6)'
                                ],
                                borderColor: [
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(255, 99, 132, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            rtl: true,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'توزيع أوقات الحضور',
                                    font: {
                                        size: 16
                                    },
                                    align: 'center'
                                },
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return 'عدد الموظفين: ' + context.raw;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'عدد الموظفين'
                                    }
                                }
                            }
                        }
                    });
                }
                
                // Create Exit Time Chart
                if (document.getElementById('exitTimeChart')) {
                    var exitCtx = document.getElementById('exitTimeChart').getContext('2d');
                    var exitTimeChart = new Chart(exitCtx, {
                        type: 'bar',
                        data: {
                            labels: ['قبل 2 ظهراً', 'بين 2-3 ظهراً', 'بين 3-4 عصراً', 'بعد 4 عصراً'],
                            datasets: [{
                                label: 'عدد الموظفين',
                                data: [before2PM, between2and3PM, between3and4PM, after4PM],
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.6)',
                                    'rgba(255, 159, 64, 0.6)',
                                    'rgba(75, 192, 192, 0.6)',
                                    'rgba(54, 162, 235, 0.6)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(255, 159, 64, 1)',
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(54, 162, 235, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            rtl: true,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'توزيع أوقات الانصراف',
                                    font: {
                                        size: 16
                                    },
                                    align: 'center'
                                },
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return 'عدد الموظفين: ' + context.raw;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'عدد الموظفين'
                                    }
                                }
                            }
                        }
                    });
                }
            } catch (err) {
                console.error("Error creating attendance charts:", err);
            }
        }
        
        function updateDepartments() {
            var dgCode = $("#Dg").val();
            var deptSelect = $("#Dept");
            
            // Clear current options
            deptSelect.empty();
            deptSelect.append('<option value="-1">-- الكل --</option>');
            
            // Reset section dropdown
            $("#Section").empty();
            $("#Section").append('<option value="0">-- الكل --</option>');
            
            if (dgCode > 0) {
                // Filter departments based on selected DG
                @foreach (var dept in Model.Departments)
                {
                    <text>
                    if (@dept.DgCode == dgCode) {
                        deptSelect.append('<option value="@dept.DeptCode">@dept.DeptDespA</option>');
                    }
                    </text>
                }
            } else {
                // Show all departments
                @foreach (var dept in Model.Departments)
                {
                    <text>
                    deptSelect.append('<option value="@dept.DeptCode">@dept.DeptDespA</option>');
                    </text>
                }
            }
        }
        
        function updateSections() {
            var dgCode = $("#Dg").val();
            var deptCode = $("#Dept").val();
            var sectionSelect = $("#Section");
            
            // Clear current options
            sectionSelect.empty();
            sectionSelect.append('<option value="0">-- الكل --</option>');
            
            if (deptCode > 0) {
                // Filter sections based on selected department
                @foreach (var s in Model.Sections)
                {
                    <text>
                    if (@s.DeptCode == deptCode && @s.DgCode == dgCode) {
                        sectionSelect.append('<option value="@s.SectionCode">@s.SectionDespA</option>');
                    }
                    </text>
                }
            } else if (dgCode > 0) {
                // Filter sections based on selected DG
                @foreach (var s in Model.Sections)
                {
                    <text>
                    if (@s.DgCode == dgCode) {
                        sectionSelect.append('<option value="@s.SectionCode">@s.SectionDespA</option>');
                    }
                    </text>
                }
            } else {
                // Show all sections
                @foreach (var s in Model.Sections)
                {
                    <text>
                    sectionSelect.append('<option value="@s.SectionCode">@s.SectionDespA</option>');
                    </text>
                }
            }
        }
        
        function sendWarningEmail(empNo, empName, date) {
            // Set modal form fields
            $("#warningEmpNo").val(empNo);
            $("#warningEmployeeName").val(empName);
            $("#warningDate").val(date);
            
            // Replace placeholders in the message template
            var message = $("#warningMessage").val();
            message = message.replace("{DATE}", date);
            $("#warningMessage").val(message);
            
            // Show the modal
            var warningModal = new bootstrap.Modal(document.getElementById('warningEmailModal'));
            warningModal.show();
        }

        function loadWarningTemplate(template) {
            var templates = [
                {
                    name: "attendence.warning1",
                    text: "@Html.Raw(Model._h.Settings.Get("email.template.attendence.warning1", ""))"
                },
                {
                    name: "attendence.warning2",
                    text: "@Html.Raw(Model._h.Settings.Get("email.template.attendence.warning2", ""))"
                }
            ];

            var template = templates.find(t => t.name === template);
            if (template) {
                $("#warningMessage").val(template.text);
            }
        }

        // Bulk Warning Functions
        function showBulkWarningModal() {
            // Load default template
            loadBulkWarningTemplate('attendence.warning1');

            // Show the modal
            var bulkWarningModal = new bootstrap.Modal(document.getElementById('bulkWarningModal'));
            bulkWarningModal.show();
        }

        function loadBulkWarningTemplate(templateName) {
            var templates = [
                {
                    name: "attendence.warning1",
                    text: "@Html.Raw(Model._h.Settings.Get("email.template.attendence.warning1", ""))"
                },
                {
                    name: "attendence.warning2",
                    text: "@Html.Raw(Model._h.Settings.Get("email.template.attendence.warning2", ""))"
                }
            ];

            var template = templates.find(t => t.name === templateName);
            if (template) {
                $("#bulkWarningMessage").val(template.text);
            }
        }

        function startBulkWarning() {
            // Validate form
            if (!$("#confirmBulkSend").is(':checked')) {
                alert('يرجى تأكيد الإرسال أولاً');
                return;
            }

            var warningMessage = $("#bulkWarningMessage").val();
            if (!warningMessage.trim()) {
                alert('يرجى إدخال رسالة التنبيه');
                return;
            }

            // Get current filter values
            var fromDate = $("#from").val();
            var toDate = $("#to").val();
            var dgCode = $("#Dg").val() || 0;
            var deptCode = $("#Dept").val() || -1;
            var sectionCode = $("#Section").val() || 0;
            var statusFilter = $("#bulkStatusFilter").val();

            // Hide bulk warning modal
            $("#bulkWarningModal").modal('hide');

            // Show progress modal
            $("#bulkWarningProgressModal").modal('show');

            // Start the bulk warning process
            $.ajax({
                url: '@Url.Action("SendBulkWarningEmails", "Attendence")',
                type: 'POST',
                data: {
                    fromDate: fromDate,
                    toDate: toDate,
                    dgCode: dgCode,
                    deptCode: deptCode,
                    sectionCode: sectionCode,
                    statusFilter: statusFilter,
                    warningMessage: warningMessage
                },
                success: function(response) {
                    if (response.success) {
                        // Start polling for progress
                        pollBulkWarningProgress(response.jobId);
                    } else {
                        alert('خطأ في بدء العملية: ' + response.message);
                        progressModal.hide();
                    }
                },
                error: function() {
                    alert('خطأ في الاتصال بالخادم');
                    progressModal.hide();
                }
            });
        }

        var bulkWarningProgressInterval;

        function pollBulkWarningProgress(jobId) {
            bulkWarningProgressInterval = setInterval(function() {
                $.ajax({
                    url: '@Url.Action("GetBulkWarningProgress", "Attendence")/' + jobId,
                    type: 'GET',
                    success: function(status) {
                        updateBulkWarningProgress(status);

                        // Stop polling if job is completed or failed
                        if (status.status === 'Completed' || status.status === 'Failed' || status.status === 'NotFound') {
                            clearInterval(bulkWarningProgressInterval);
                            $("#bulkProgressCloseBtn").prop('disabled', false);
                        }
                    },
                    error: function() {
                        console.error('Error polling bulk warning progress');
                    }
                });
            }, 2000); // Poll every 2 seconds
        }

        function updateBulkWarningProgress(status) {
            // Update status badge
            var statusBadge = $("#bulkJobStatus");
            var statusText = '';
            var statusClass = '';

            switch(status.status) {
                case 'Initializing':
                    statusText = 'جاري التحضير...';
                    statusClass = 'bg-info';
                    break;
                case 'Processing':
                    statusText = 'جاري المعالجة...';
                    statusClass = 'bg-warning';
                    break;
                case 'Completed':
                    statusText = 'مكتملة';
                    statusClass = 'bg-success';
                    $("#bulkCompletedSection").show();
                    break;
                case 'Failed':
                    statusText = 'فشلت';
                    statusClass = 'bg-danger';
                    break;
                case 'NotFound':
                    statusText = 'غير موجودة';
                    statusClass = 'bg-secondary';
                    break;
                default:
                    statusText = status.status;
                    statusClass = 'bg-secondary';
            }

            statusBadge.removeClass().addClass('badge ' + statusClass).text(statusText);

            // Update progress bar
            var progress = status.progress || 0;
            $("#bulkProgressBar").css('width', progress + '%').attr('aria-valuenow', progress).text(progress + '%');

            // Update progress text
            $("#bulkProgressText").text(status.processedEmployees + ' من ' + status.totalEmployees + ' موظف');

            // Update counters
            $("#bulkTotalEmployees").text(status.totalEmployees || 0);
            $("#bulkSuccessfulEmails").text(status.successfulEmails || 0);
            $("#bulkFailedEmails").text(status.failedEmails || 0);

            // Show errors if any
            if (status.errors && status.errors.length > 0) {
                $("#bulkErrorsSection").show();
                $("#bulkErrorsList").html(status.errors.join('<br>'));
            }
        }

        // Test function for bulk warning system
        function testBulkWarningSetup() {
            $.ajax({
                url: '@Url.Action("TestBulkWarningSetup", "Attendence")',
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        var results = response.testResults;
                        var message = 'نتائج اختبار النظام:\n\n';
                        message += 'خدمة البريد الإلكتروني: ' + (results.emailServiceAvailable ? 'متوفرة' : 'غير متوفرة') + '\n';
                        message += 'خدمة التخزين المؤقت: ' + (results.cacheServiceAvailable ? 'متوفرة' : 'غير متوفرة') + '\n';
                        message += 'اتصال قاعدة البيانات: ' + (results.databaseConnectionActive ? 'نشط' : 'غير نشط') + '\n';
                        message += 'عدد سجلات الحضور: ' + results.attendanceRecordsCount + '\n';
                        message += 'عدد الموظفين: ' + results.employeesCount + '\n';
                        message += 'عدد التنبيهات الموجودة: ' + results.existingWarningsCount + '\n';
                        message += 'التخزين المؤقت: ' + (results.memoryCacheWorking ? 'يعمل' : 'لا يعمل') + '\n';
                        message += 'قوالب البريد الإلكتروني: ' + (results.emailTemplatesAvailable ? 'متوفرة' : 'غير متوفرة') + '\n';
                        message += '\nالنظام: ' + (results.systemReady ? 'جاهز للعمل' : 'غير جاهز');

                        alert(message);
                    } else {
                        alert('خطأ في اختبار النظام: ' + response.message);
                    }
                },
                error: function() {
                    alert('خطأ في الاتصال بالخادم أثناء اختبار النظام');
                }
            });
        }
    </script>
} 