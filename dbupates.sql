
  CREATE TABLE "DRCH"."SETTINGS" 
   (	"KEY" VARCHAR2(200 BYTE) NOT NULL ENABLE, 
	"VALUE" VARCHAR2(1000 BYTE), 
	 CONSTRAINT "SETTINGS_PK" PRIMARY KEY ("KEY")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 
  TABLESPACE "TS_DATA1"  ENABLE
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "TS_DATA1" ;




  CREATE OR REPLACE FORCE EDITIONABLE VIEW "DRCH"."VEMP_WORK_HOURS_NEW" ("<PERSON><PERSON>_<PERSON>", "WORK_DATE", "FIRST_ENTRY", "LAST_ENTRY", "WORK_DURATION", "WORK_HOURS_IN_SHIFT", "OVERTIME_HOURS", "IS_HOLIDAY") AS 
  (
  SELECT 
    EMP_NO,
    WOR<PERSON>_<PERSON>AT<PERSON>,
    FIRST_ENTRY,
    LAST_ENTRY,
    ROUND(WORK_DURATION * 24, 2) AS WORK_DURATION,
    ROUND(WORK_HOURS_IN_SHIFT * 24, 2) AS WORK_HOURS_IN_SHIFT,
    ROUND(OVERTIME_HOURS * 24, 2) AS OVERTIME_HOURS,
    IS_HOLIDAY
  FROM (
    SELECT 
      (EMP_NO * 1) as EMP_NO,
      TRUNC(TRANS_DATETIME) AS WORK_DATE,
      MIN(TRANS_DATETIME) AS FIRST_ENTRY,
      MAX(TRANS_DATETIME) AS LAST_ENTRY,
      CASE
        WHEN COUNT(*) = 1 THEN 5 / (24 * 60) -- 5 minutes
        ELSE MAX(TRANS_DATETIME) - MIN(TRANS_DATETIME)
      END AS WORK_DURATION,
      CASE
        WHEN COUNT(*) = 1 THEN 5 / (24 * 60)
        ELSE GREATEST(0, 
          LEAST(MAX(TRANS_DATETIME), 
            LEAST(
              GREATEST(MIN(TRANS_DATETIME), TRUNC(TRANS_DATETIME) + 7.5/24),
              TRUNC(TRANS_DATETIME) + 8.5/24
            ) + 7/24
          ) - 
          LEAST(
            GREATEST(MIN(TRANS_DATETIME), TRUNC(TRANS_DATETIME) + 7.5/24),
            TRUNC(TRANS_DATETIME) + 8.5/24
          )
        )
      END AS WORK_HOURS_IN_SHIFT,
      CASE 
        WHEN 
          TO_CHAR(TRUNC(TRANS_DATETIME), 'DY', 'NLS_DATE_LANGUAGE=ENGLISH') IN ('FRI','SAT')
          OR TRUNC(TRANS_DATETIME) IN (SELECT HOLIDAY FROM THOLIDAYS WHERE HOLIDAY=TRUNC(TRANS_DATETIME)) 
        THEN
          GREATEST(0, 
            LEAST(MAX(TRANS_DATETIME), TRUNC(TRANS_DATETIME) + 1) - 
            GREATEST(MIN(TRANS_DATETIME), TRUNC(TRANS_DATETIME) + 7.5/24)
          )
        ELSE 
          GREATEST(0, 
            MAX(TRANS_DATETIME) - (
              LEAST(
                GREATEST(MIN(TRANS_DATETIME), TRUNC(TRANS_DATETIME) + 7.5/24),
                TRUNC(TRANS_DATETIME) + 8.5/24
              ) + 7/24
            )
          )
      END AS OVERTIME_HOURS,
      CASE 
        WHEN TRUNC(TRANS_DATETIME) IN (SELECT HOLIDAY FROM THOLIDAYS WHERE HOLIDAY=TRUNC(TRANS_DATETIME)) THEN 1
        ELSE 0
      END AS IS_HOLIDAY
    FROM 
      FP.t_origin_trans
    WHERE 
      TRANS_DATETIME >= TRUNC(TRANS_DATETIME) + (3/24) AND EMP_NO < 2000
    GROUP BY 
      EMP_NO, TRUNC(TRANS_DATETIME)
  )
);




  CREATE TABLE "DRCH"."TFILES" 
   (	"GUID" VARCHAR2(100 BYTE) NOT NULL ENABLE, 
	"LABEL" VARCHAR2(200 BYTE) NOT NULL ENABLE, 
	"NAME" VARCHAR2(300 BYTE), 
	"TIMESTAMP" DATE, 
	 CONSTRAINT "TFILES_PK" PRIMARY KEY ("GUID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 
  TABLESPACE "TS_DATA1"  ENABLE
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "TS_DATA1" ;


  

ALTER TABLE DRCH.INVENTORY_ITEM 
ADD (FILE_GUID VARCHAR2(100) )

ALTER TABLE DRCH.TEMPLOYEE_MAS
ADD (FILE_GUID VARCHAR2(100));

ALTER TABLE DRCH.CON_EMPLOYEE_MAS
ADD (FILE_GUID VARCHAR2(100) );


ALTER TABLE DRCH.PURCHASE_QUOTATIONS
ADD (FILE_GUID VARCHAR2(100) );


ALTER TABLE DRCH.TRAINIG_COMM
ADD (FILE_GUID VARCHAR2(100) );


ALTER TABLE DRCH.TEMP_TRG_REQ
ADD (FILE_GUID VARCHAR2(100) );



  CREATE OR REPLACE FORCE EDITIONABLE VIEW "DRCH"."VCON_DTLS" ("UNIT_CODE", "UNIT_DESP_A", "EMP_NO", "EMP_NAME_A", "EMP_NAME_E", "DG_CODE", "DG_DESP_A", "DEPT_CODE", "DEPT_DESP_A", "SECTION_CODE", "DESGN_CODE", "CAT_CODE", "SUB_CAT_CODE", "GRADE_RANK_CODE", "OMAN_PHONE_NO", "CUR_ADRS1_A", "CUR_ADRS1_E", "SEX_CODE", "MAR_STAT_CODE", "COUNTRY_CODE", "NATIONALITY_CODE", "APPOINT_DATE", "C_EMP_STAT", "STAT_ON_DATE", "LAST_PAID_DATE", "PAY_REVIEW_DATE", "PAY_LOCN_CODE", "PAY_MODE", "BANK_CODE", "BRANCH_CODE", "BANK_ACC_NO", "DG_DESP_E", "DEPT_DESP_E", "BIRTH_DATE", "GRADE_RANK", "FILE_GUID", "DESG_CODE", "DESG_TYPE", "EMAIL_ID", "SEXINFO", "NAT_ID", "EMP_NAME_A1", "SERVES", "BRANCH_NAME_A", "BANK_NAME_A", "NDATE_DESG", "DESGN_HIER_LEVEL", "GSC_APPOINT_DATE") AS 
  (SELECT t1.unit_code, t3.unit_desp_a, t1.emp_no, emp_name_a, emp_name_e,
        t1.dg_code, t4.dg_desp_a, t1.dept_code, t5.dept_desp_a,
        t1.section_code, t1.desgn_code, 1 AS cat_code, 1 AS sub_cat_code,
        0 AS grade_rank_code, t2.oman_phone_no, t2.cur_adrs1_a,
        t2.cur_adrs1_e, t2.sex_code, t2.mar_stat_code,
        t2.nationality_code AS country_code, t2.nationality_code,
        t2.appoint_date, t2.c_emp_stat, t2.appoint_date AS stat_on_date,
        t1.last_paid_date, t1.pay_review_date, t1.dg_code AS pay_locn_code,
        1 AS pay_mode, t2.bank_code, t2.branch_code, t2.bank_acc_no,
        t4.dg_desp_e, t5.dept_desp_e, t2.birth_date,
        t6.cont_type_desp_a AS grade_rank,
        t2.FILE_GUID,
        DECODE (t2.sex_code,
                1, t7.desgn_desp_m_a,
                t7.desgn_desp_f_a
               ) AS desg_code,
        t7.DESGN_TYPE AS desg_type, t2.email_id,
        DECODE (t2.sex_code, 1, 'ذكر', 'إنثى') sexinfo,
        t2.civil_number AS nat_id, emp_name_e AS emp_name_a1,
           TRUNC (((SYSDATE) - appoint_date) / 365)
        || ' , '
        || TRUNC (  (MONTHS_BETWEEN ((SYSDATE), appoint_date))
                  - (TRUNC (((SYSDATE) - appoint_date) / 365) * 12)
                 ) serves,
        tc.branch_name_a, tb.bank_name_a, t2.appoint_date AS ndate_desg,
        t7.DESGN_TYPE AS desgn_hier_level,t2.appoint_date as GSC_APPOINT_DATE 
   FROM con_contract t1,
        con_employee_mas t2,
        tunit_code t3,
        tdg_code t4,
        tdept_code t5,
        con_contract_type t6,
        tdesgn_code t7,
        tbank_code tb,
        tbranch_code tc
  WHERE t1.emp_no = t2.emp_no
    AND t2.c_emp_stat IN (35, 37, 38)
    AND t1.unit_code = t3.unit_code
    AND t1.dg_code = t4.dg_code(+)
    AND t1.unit_code = t5.unit_code(+)
    AND t1.dg_code = t5.dg_code(+)
    AND t1.dept_code = t5.dept_code(+)
    AND t1.contract_type = t6.cont_type
    AND t1.desgn_code = t7.desgn_code
    AND t2.bank_code = tb.bank_code
    AND t2.bank_code = tc.bank_code(+)
    AND t2.branch_code = tc.branch_code(+))
 ;


  CREATE OR REPLACE VIEW "DRCH"."VEMP_DTLS" ("UNIT_CODE", "UNIT_DESP_A", "EMP_NO", "EMP_NAME_A", "EMP_NAME_E", "DG_CODE", "DG_DESP_A", "DEPT_CODE", "DEPT_DESP_A", "SECTION_CODE", "DESGN_CODE", "CAT_CODE", "SUB_CAT_CODE", "GRADE_RANK_CODE", "OMAN_PHONE_NO", "CUR_ADRS1_A", "CUR_ADRS1_E", "SEX_CODE", "MAR_STAT_CODE", "COUNTRY_CODE", "NATIONALITY_CODE", "APPOINT_DATE", "C_EMP_STAT", "STAT_ON_DATE", "LAST_PAID_DATE", "PAY_REVIEW_DATE", "PAY_LOCN_CODE", "PAY_MODE", "BANK_CODE", "BRANCH_CODE", "BANK_ACC_NO", "DG_DESP_E", "DEPT_DESP_E", "BIRTH_DATE", "GRADE_RANK", "DESG_CODE", "DESG_TYPE", "EMAIL_ID", "SEXINFO", "NAT_ID", "EMP_NAME_A1", "SERVES", "BRANCH_NAME_A", "BANK_NAME_A", "NDATE_DESG", "DESGN_HIER_LEVEL", "GSC_APPOINT_DATE", "PROFILE_IMAGE") AS 
  (select T1.UNIT_CODE, T3.UNIT_DESP_A,
 T1.EMP_NO,
 trim(T2.TITLE_DESP_A || ' / ' || T1.EMP_NAM1_A || ' ' ||
                     DECODE(T1.NATIONALITY_CODE,  968, DECODE(T1.NATIONALITY_ACQUIRD_BY, 0,
                     DECODE(T1.SEX_CODE, 1, ' بن ', ' بنت ') ||  T1.EMP_NAM2_A || ' ' ||
                     DECODE(T1.EMP_NAM3_A , '', '', ' بن '), T1.EMP_NAM2_A), T1.EMP_NAM2_A || ' ' ) ||
                      T1.EMP_NAM3_A || ' ' || T1.EMP_TRIB_NAM4_A) EMP_NAME_A,
 trim(T2.TITLE_DESP_E || ' ' || T1.EMP_NAM1_E || ' ' ||
                     DECODE(T1.NATIONALITY_CODE,  968, DECODE(T1.NATIONALITY_ACQUIRD_BY, 0,
                     DECODE(T1.SEX_CODE, 1, ' Bin ', ' Bint ') ||  T1.EMP_NAM2_E ||
                     DECODE(T1.EMP_NAM3_E , '', '', ' Bin '), T1.EMP_NAM2_E), T1.EMP_NAM2_E || ' ' ) ||
                      T1.EMP_NAM3_E || ' ' || T1.EMP_TRIB_NAM4_E) EMP_NAME_E,                                          
 T1.DG_CODE, T4.DG_DESP_A,
 T1.DEPT_CODE, T5.DEPT_DESP_A,
 T1.SECTION_CODE,
 T1.DESGN_CODE,
 T1.CAT_CODE,
 T1.SUB_CAT_CODE,
 T1.GRADE_RANK_CODE,
 DECODE( T1.OMAN_PHONE_NO, null,T1.RES_PHONE_NO,T1.OMAN_PHONE_NO  ) as  OMAN_PHONE_NO,  
 DECODE(  T1.CUR_ADRS1_A, null,T1.PERM_ADRS1_A, T1.CUR_ADRS1_A  ) as  CUR_ADRS1_A ,
 DECODE(  T1.CUR_ADRS1_E, null,T1.PERM_ADRS1_E, T1.CUR_ADRS1_E  ) as   CUR_ADRS1_E ,
 T1.SEX_CODE,
 T1.MAR_STAT_CODE,
 T1.COUNTRY_CODE,
 T1.NATIONALITY_CODE,
 T1.APPOINT_DATE,
 T1.C_EMP_STAT,
 T1.STAT_ON_DATE,
 T1.LAST_PAID_DATE,
 T1.PAY_REVIEW_DATE,
 T1.PAY_LOCN_CODE,
 T1.PAY_MODE,
 T1.BANK_CODE,
 T1.BRANCH_CODE,
 T1.BANK_ACC_NO ,
 T4.DG_DESP_e,
 T5.DEPT_DESP_e  ,
 T1.BIRTH_DATE,
t6.grade_rank_desp_a,
DECODE(T1.SEX_CODE, 1,t7.DESGN_DESP_M_A,t7.DESGN_DESP_f_A  )   ,
t8.desgn_type,
 t1.emp_no||t1.emp_nam1_e,
 DECODE(T1.SEX_CODE, 1,'ذكر','انثى'  ) SEXINFO,
 T1.NATIONALITY_ID_NO ,
 trim(T1.EMP_NAM1_A || ' ' ||
                     DECODE(T1.NATIONALITY_CODE,  968, DECODE(T1.NATIONALITY_ACQUIRD_BY, 0,
                     DECODE(T1.SEX_CODE, 1, 'بن ', ' بنت ') ||  T1.EMP_NAM2_A || ' ' ||
                     DECODE(T1.EMP_NAM3_A , '', '', ' بن '), T1.EMP_NAM2_A), T1.EMP_NAM2_A || ' ' ) ||
                      T1.EMP_NAM3_A || ' ' || T1.EMP_TRIB_NAM4_A) emp_name_a1,
                      trunc(((sysdate)-appoint_date)/365) ||' , '|| trunc((months_between((sysdate),appoint_date))-(trunc(((sysdate)-appoint_date)/365)*12))  serves    
                      , TC.BRANCH_NAME_A,
 TB.BANK_NAME_A  , 

 (SELECT MAX (t3.from_date)
                   FROM temployee_career t3
                  WHERE (   order_type = 88
                         OR order_type = 58
                         OR order_type = 106
                         OR order_type = 386
                         OR order_type = 8
                         OR order_type = 92
                        )
                    AND (t3.emp_no = t1.emp_no) and T1.DESGN_CODE= t3.DESGN_CODE) AS ndate_desg  ,t8.desgn_hier_level ,
                    (select r.FROM_DATE
from TEMPLOYEE_CAREER r
where r.emp_no =T1.EMP_NO and P_UNIT_CODE=1 and r.ORDER_DATE = (select min(ORDER_DATE) from TEMPLOYEE_CAREER where emp_no = r.emp_no and P_UNIT_CODE=1)) as GSC_APPOINT_DATE, T1.FILE_GUID as PROFILE_IMAGE

 FROM TEMPLOYEE_MAS T1,TTITLE_CODE T2, TUNIT_CODE T3,
      TDG_CODE T4, TDEPT_CODE T5 ,tgrade_rank_code t6 ,
      tdesgn_code t7  ,tdesgn_type t8 , 
      TBANK_CODE TB, TBRANCH_CODE TC
 WHERE T1.UNIT_CODE = 1 AND
       T1.C_EMP_STAT IN (35,37,38) AND
       T1.EMP_TITLE_CODE = T2.TITLE_CODE(+) AND
       T1.UNIT_CODE  = T3.UNIT_CODE AND
       T1.UNIT_CODE  = T4.UNIT_CODE AND
       T1.DG_CODE    = T4.DG_CODE   AND
       T1.UNIT_CODE  = T5.UNIT_CODE AND
       T1.DG_CODE    = T5.DG_CODE   AND
       T1.DEPT_CODE  = T5.DEPT_CODE   and
       t1.grade_rank_code=t6.grade_rank_code   
       and t1.desgn_code = t7.desgn_code 
       and t7.desgn_type = t8.desgn_type   
 AND TC.BANK_CODE = TB.BANK_CODE
 AND T1.BANK_CODE = TB.BANK_CODE
 AND T1.BANK_CODE = TC.BANK_CODE
 AND T1.BRANCH_CODE = TC.BRANCH_CODE  )


 UNION
 (select "UNIT_CODE","UNIT_DESP_A","EMP_NO","EMP_NAME_A","EMP_NAME_E",
 "DG_CODE","DG_DESP_A","DEPT_CODE","DEPT_DESP_A","SECTION_CODE","DESGN_CODE","CAT_CODE","SUB_CAT_CODE","GRADE_RANK_CODE","OMAN_PHONE_NO",
 "CUR_ADRS1_A","CUR_ADRS1_E","SEX_CODE","MAR_STAT_CODE","COUNTRY_CODE","NATIONALITY_CODE","APPOINT_DATE","C_EMP_STAT","STAT_ON_DATE","LAST_PAID_DATE",
 "PAY_REVIEW_DATE","PAY_LOCN_CODE","PAY_MODE","BANK_CODE","BRANCH_CODE","BANK_ACC_NO","DG_DESP_E","DEPT_DESP_E","BIRTH_DATE","GRADE_RANK","DESG_CODE","DESG_TYPE","EMAIL_ID","SEXINFO",
 "NAT_ID","EMP_NAME_A1","SERVES","BRANCH_NAME_A","BANK_NAME_A","NDATE_DESG","DESGN_HIER_LEVEL","GSC_APPOINT_DATE","FILE_GUID" as PROFILE_IMAGE from VCON_DTLS)




WITH READ ONLY 
 ;


ALTER TABLE DRCH.TLEAVE_APL_TXS
ADD (FILE_GUID VARCHAR2(100));




  CREATE TABLE "SYS"."TATTENEDENCE_WARNINGS" 
   (	"EMP_NO" NUMBER(5,0) NOT NULL ENABLE, 
	"ATTENDENCE_DATE" DATE NOT NULL ENABLE, 
	 CONSTRAINT "TATTNEDENCE_WARNINGS_PK" PRIMARY KEY ("EMP_NO", "ATTENDENCE_DATE")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "SYSTEM"  ENABLE
   ) PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "SYSTEM" ;




  CREATE TABLE "DRCH"."EMPLOYEE_WARNINGS" 
   (	"GUID" VARCHAR2(100 BYTE) NOT NULL ENABLE, 
	"TITLE" VARCHAR2(100 BYTE), 
	"BODY" VARCHAR2(1000 BYTE), 
	"WARNING_DATE" DATE, 
	"EMP_NO" NUMBER(6,0), 
	"TIMESTAMP" DATE, 
	 CONSTRAINT "EMPLOYEE_WARNING_PK" PRIMARY KEY ("GUID")
  USING INDEX (CREATE UNIQUE INDEX "DRCH"."EMPLOYEE_WARNING_PK" ON "DRCH"."EMPLOYEE_WARNINGS" ("GUID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 
  TABLESPACE "TS_DATA1" )  ENABLE
   )