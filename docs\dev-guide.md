# Developer Guide

<!-- META: Human Resource Management System Developer Documentation -->
<!-- KEYWORDS: documentation, development, guidelines, standards, practices, controllers, database, API, frontend, features -->

## Documentation Rules
<!-- SECTION: Documentation Rules and Structure -->
<!-- PURPOSE: Guidelines for maintaining documentation -->

1. **Documentation First**: Before implementing any feature, ensure related documentation exists or update it.
   <!-- KEYWORD: documentation-first approach -->

2. **Update Documentation**: After implementing a feature, update all relevant documentation files.
   <!-- KEYWORD: documentation updates -->

3. **Documentation Structure**:
   <!-- SUBSECTION: Documentation Structure -->
   - `architecture/`: System design, components, and diagrams
   - `database/`: Connection specifications, schema design, queries
   - `api/`: API endpoints, authentication, request/response formats
   - `frontend/`: UI components, forms, styling guidelines
   - `leaves/`: Leave management and calculations
   - `attendance/`: Attendance tracking and reporting
   - `overtime/`: Overtime management and financial tracking
   - `system/`: System utilities, services, and infrastructure components

4. **Documentation Format**: Each document should include:
   <!-- SUBSECTION: Documentation Format -->
   - Overview
   - Technical specifications
   - Usage examples
   - Reference links
   
5. **Cross-referencing**: Link related documents to establish connections between components.
   <!-- KEYWORD: cross-references, document linking -->

## Key Documentation Files
<!-- SECTION: Documentation Index -->
<!-- PURPOSE: Central index of all documentation files -->

### Architecture Documents
<!-- CATEGORY: Architecture -->
- **System Overview**: [System Architecture Overview](architecture/system-overview.md)
- **Database Schema**: [Database Schema](architecture/database-schema.md)
- **API Documentation**: [API Documentation](architecture/api-documentation.md)
- **Namespace Map**: [Modules Namespace Map](architecture/namespace-map.md)
- **Modular Context Architecture**: [Modular Context Architecture](architecture/modular-context-architecture.md) - Entity Framework modular configuration system

### Database Documents
<!-- CATEGORY: Database -->
- **Database Connections**: [Database Connection Specifications](database/connection-spec.md)
- **Database Models**: [Database Models Documentation](database/models.md)

### Frontend Documents
<!-- CATEGORY: Frontend -->
- **Form Submission**: [Form Submission Guidelines](frontend/form-submission-spec.md)
- **DataTable System**: [DataTable Specification](frontend/datatable-spec.md)
- **Filtering System**: [Filtering System Documentation](filtering-system.md)

### API Documents
<!-- CATEGORY: API -->
- **API Endpoints**: [API Endpoints Reference](api/endpoints.md)

### Feature Module Documents
<!-- CATEGORY: Features -->

#### Leave Management
<!-- SUBCATEGORY: Leave Management -->
- **Leave Calculations**: [Leave Calculation Specifications](leaves/leave-calculation-spec.md)
- **Leave UI**: [Leave Management UI](leaves/leave-management-ui.md)
- **Leave Reports**: [Leave Reports Filtering System](leaves/leave-reports.md) - Hierarchical filtering for leave reports with DG and department cascading dropdowns
- **Excuse Policy**: [Excuse Policy](leaves/excuse-policy.md)
- **Leave Balance Adjustment**: [Leave Balance Adjustment](features/leave-balance-adjustment.md) - Managing manual adjustments to employee leave balances
- **Leave Balance Administration**: [Leave Balance Administration](features/leave-balance-admin.md) - Direct balance updates for administrators without workflow approval
- **Leave Return Management**: [Leave Return Management](features/leave-return-management.md) - Processing early returns from leave with automatic balance adjustments
- **Leave Module Migration**: [Leaves Module Migration Plan](implementation/leaves-module-migration-plan.md) - Comprehensive plan for migrating the Leaves module to modular architecture
- **Interface Restructuring**: [Interface Directory Restructuring Plan](implementation/interface-restructuring-plan.md) - Plan for restructuring the Interface directory to align with modular architecture
- **Core Context Integration**: [Core Context Integration Documentation](implementation/core-context-integration.md) - How the UI infrastructure integrates with existing Core context system

#### Overtime Management
<!-- SUBCATEGORY: Overtime Management -->
- **Overtime Module Documentation**: [Overtime Module Comprehensive Guide](../html/overtime-module-documentation.html) - Complete overtime management system with workflows, rate calculations, multi-level approvals, and financial tracking
- **Overtime Controller Architecture**: [Overtime Controller Refactoring](features/overtime-controller-refactoring.md) - Role-based controller architecture for overtime management
- **Overtime Finance Trends**: [Overtime Finance Trend Analysis](features/overtime-finance-trends.md) - Comprehensive trend analysis for finance manager with current vs last month comparisons and pending payment tracking
- **Finance Requests Improvements**: [Finance Requests Page Improvements](features/finance-requests-improvements.md) - Enhanced payment status filtering tabs and improved month sorting functionality
- **Overtime File Upload**: [Overtime Request File Upload](features/overtime-file-upload.md) - File attachment functionality for department overtime request creation
- **Overtime Return Unification**: [Overtime Return Endpoints Unification](features/overtime-return-unification.md) - Unified method for handling all overtime request return endpoints
- **Settings Controllers Refactoring**: [Settings Controllers Refactoring](features/settings-controllers-refactoring.md) - Split monolithic SettingsController into specialized System, Email, and EmailTemplates controllers

#### Transport Management
<!-- SUBCATEGORY: Transport Management -->
- **Transport Module Documentation**: [Transport Module Comprehensive Guide](transport-module.md) - Complete fleet management system with vehicle allocation, maintenance tracking, fuel management, violation records, and multi-level approval workflows with Purchases module integration

#### Attendance Management
<!-- SUBCATEGORY: Attendance Management -->
- **Attendance Management**: [Attendance Management Guide](attendance/attendance-management.md)
- **Attendance Reports**: [Attendance Reporting System](attendance/attendance-reports.md)
- **Warning System**: [Attendance Warning System](attendance/attendance-reports.md#warning-email-system)
- **Bulk Warning System**: [Bulk Warning Notifications](attendance/attendance-reports.md#bulk-warning-system) - Background processing for mass warning notifications
- **Bulk Warning Implementation**: [Implementation Summary](attendance/bulk-warning-implementation-summary.md) - Complete implementation details and technical specifications
- **Manager Reports**: [Manager Reporting System](attendance/manager-reports.md)
- **Excuse Management**: [Excuse Management Module](attendance/excuse-management-module.md)
- **Overtime Reports**: [Overtime Reporting System](attendance/overtime-reports.md)

### System Services Documents
<!-- CATEGORY: System Services -->
- **Settings Service**: [Settings Service Documentation](system/settings-service.md)
- **Email Service**: [Email Service Documentation](system/email-service.md) <!-- RELATED: notifications -->
- **Email Templates**: [Email Templates System](features/email-templates.md) <!-- RELATED: email, notifications -->
- **Desktop Notifications**: [Desktop Notifications Documentation](system/desktop-notifications.md)
- **File Storage System**: [File Storage System Documentation](system/file-storage.md)
- **Response Helper**: [Response Helper Documentation](system/response-helper.md) <!-- RELATED: ajax, forms -->
- **User Rights Caching**: [User Rights Caching System](features/user-rights-caching.md) <!-- RELATED: security, performance -->
- **Task Provider System**: [Task Provider System](system/task-providers.md) <!-- RELATED: dashboard, user tasks, modularity -->
- **Task Provider Registration**: [Task Provider Registration Implementation](implementation/task-provider-registration.md) <!-- RELATED: dependency injection, module configuration -->

### HTML Documentation
<!-- CATEGORY: HTML Documentation -->
- **Documentation Hub**: [HR System Documentation Hub](../html/documentation-home.html) - Central navigation hub for all HR system documentation modules
- **Leaves Module**: [Leaves Module Documentation](../html/leaves-module-documentation.html) - Comprehensive leave management system documentation
- **Leaves Developer Guide**: [Leaves Developer Guide](../html/leaves-developer-guide.html) - Detailed technical documentation for developers working with the leave management system
- **Leaves User Manual (Arabic)**: [Leaves User Manual](../html/leaves-user-manual-arabic.html) - دليل المستخدم العربي لنظام الإجازات للموظفين
- **Leaves Department Manual (Arabic)**: [Leaves Department Manual](../html/leaves-department-manual-arabic.html) - دليل قسم الإجازات العربي لموظفي شؤون الموظفين
- **Overtime Module**: [Overtime Module Documentation](../html/overtime-module-documentation.html) - Complete overtime management system guide with workflows and technical specifications

## Update Process
<!-- SECTION: Documentation Update Process -->
<!-- PURPOSE: Guidelines for maintaining documentation during development -->

When implementing or modifying features:
1. Identify all affected documentation files
2. Update technical specifications and examples
3. Add new documentation if necessary
4. Reference updated documentation in commit messages

# Development Guide
<!-- SECTION: Development Standards and Practices -->
<!-- PURPOSE: Technical implementation guidelines -->

This document outlines key development guidelines and features for the Human Resource Management System.

## Development Best Practices
<!-- SECTION: Development Best Practices -->
<!-- KEYWORDS: development, implementation, standards -->

For MVC controller development best practices and routing guidelines, see [System Architecture Overview](architecture/system-overview.md#mvc-controller-implementation).

## Feature Areas
<!-- SECTION: Feature Areas -->
<!-- PURPOSE: Overview of system modules and capabilities -->

### Employee Management
<!-- SUBSECTION: Employee Management Feature -->
<!-- KEYWORDS: employee, profile, organization structure -->
Employee management features include profile management, organizational structure, and employee details.

For detailed documentation on employee management, see:
- [System Architecture Overview](architecture/system-overview.md)
- [API Endpoints Reference](api/endpoints.md)

### Attendance Management
<!-- SUBSECTION: Attendance Management Feature -->
<!-- KEYWORDS: attendance, tracking, reporting, absence -->
Attendance tracking and reporting includes daily attendance, absence tracking, and comprehensive reports.

For detailed documentation on attendance management, see:
- [Attendance Management Guide](attendance/attendance-management.md)
- [Attendance Reporting System](attendance/attendance-reports.md)
- [Manager Reporting System](attendance/manager-reports.md)
- [Excuse Management Module](attendance/excuse-management-module.md)

### Leave Management
<!-- SUBSECTION: Leave Management Feature -->
<!-- KEYWORDS: leave, vacation, absence, request, approval, balance -->
Leave management includes request workflows, approvals, and balance tracking.

For detailed documentation on leave management, see:
- [Leave Calculation Specifications](leaves/leave-calculation-spec.md)
- [Leave Management UI](leaves/leave-management-ui.md)
- [Leave Reports Filtering System](leaves/leave-reports.md)
- [Excuse Policy](leaves/excuse-policy.md)
- [Leave Balance Administration](features/leave-balance-admin.md)
- [Leave Return Management](features/leave-return-management.md)

### Notification System
<!-- SUBSECTION: Notification System Feature -->
<!-- KEYWORDS: notifications, alerts, email, desktop -->
Notification system includes in-app notifications and email notifications for various system events.

For detailed documentation on the notification system, see:
- [Email Service Documentation](system/email-service.md) (includes notification functionality)
- [Email Templates System](features/email-templates.md) (customizable email templates)
- [Desktop Notifications Documentation](system/desktop-notifications.md)

## Technical Guidelines
<!-- SECTION: Technical Guidelines -->
<!-- PURPOSE: Implementation standards and references -->

### Database Access
<!-- SUBSECTION: Database Access -->
<!-- KEYWORDS: database, connection, queries -->
For database connection and access guidelines, see [Database Connection Specifications](database/connection-spec.md).

### Form Implementation
<!-- SUBSECTION: Form Implementation -->
<!-- KEYWORDS: forms, validation, submission -->
For form implementation guidelines, see [Form Submission Guidelines](frontend/form-submission-spec.md).

### API Development
<!-- SUBSECTION: API Development -->
<!-- KEYWORDS: API, endpoints, requests, responses -->
For API development standards, see [API Endpoints Reference](api/endpoints.md).

### System Utilities
<!-- SUBSECTION: System Utilities -->
<!-- KEYWORDS: utilities, services, settings, storage, email -->
For information on system utilities:
- **Settings Service**: [Settings Service Documentation](system/settings-service.md) <!-- DESCRIPTION: Key-value storage for application settings -->
- **Email Service**: [Email Service Documentation](system/email-service.md) <!-- DESCRIPTION: Email sending capabilities and notifications -->
- **Desktop Notifications**: [Desktop Notifications Documentation](system/desktop-notifications.md) <!-- DESCRIPTION: Local browser notifications -->
- **File Storage System**: [File Storage System Documentation](system/file-storage.md) <!-- DESCRIPTION: Organized file upload and storage system -->

## Cache Management
### User Rights Cache
- User rights are cached in the session using key format `UserRights_{EmpNo}`
- Cache is automatically cleared when:
  - A user's roles are updated in `UsersController.Update`
  - A role's rights are updated in `UsersController.RoleUpdate`
- This ensures that permission changes take effect immediately without requiring users to log out and back in
- For detailed documentation, see [User Rights Caching System](features/user-rights-caching.md)

## Feature Documentation
- See specific feature documentation in the `/docs/features` directory

## Templates
- Feature documentation templates available in `/docs/templates`

# Development Guide Index

## Architecture Documentation
- [System Architecture](architecture/system-overview.md)
- [Database Schema](architecture/database-schema.md)
- [API Documentation](architecture/api-documentation.md)

## Feature Documentation
- [Leave Management System](features/leave-management.md)
- [Employee Management](features/employee-management.md)
- [Overtime System](features/overtime-system.md)
- **[Overtime Controller Refactoring Plan](features/overtime-refactoring-plan.md)** ⭐ **NEW**

## Development Guidelines
- [Coding Standards](guidelines/coding-standards.md)
- [Testing Guidelines](guidelines/testing.md)
- [Security Guidelines](guidelines/security.md)

## Refactoring Plans
- **[OverTime Controller Refactoring](features/overtime-refactoring-plan.md)** - Breaking down the 5,205-line controller
- [Leave Controller Optimization](features/leave-controller-optimization.md) - Next target for refactoring

## Implementation Examples
- [Service Layer Implementation](examples/service-layer-example.md)
- [Controller Splitting Example](examples/controller-splitting-example.md)

## Quick Reference
- [Common Patterns](reference/common-patterns.md)
- [Utility Classes](reference/utility-classes.md)
- [Constants and Enums](reference/constants.md)