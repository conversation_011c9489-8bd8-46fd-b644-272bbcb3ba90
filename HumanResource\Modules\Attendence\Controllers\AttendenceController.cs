﻿using HumanResource.Core.Helpers.Attributes;
using Microsoft.AspNetCore.Mvc;
using HumanResource.Modules.Attendence.Models.Entities;
using Microsoft.EntityFrameworkCore;
using HumanResource.Modules.Attendence.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Modules.Shared.Services;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Employees.Models.Entities;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;

namespace HumanResource.Modules.Attendence.Controllers;


[Area("Attendence")]
[Route("Attendence")]
[Can("attendence")]
public class AttendenceController : BaseController
{

    public AttendenceViewModel _v;
    private readonly EmailService _emailService;
    private readonly ILogger<AttendenceController> _logger;
    private readonly IMemoryCache _cache;
    private readonly IServiceProvider _serviceProvider;

    public AttendenceController(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor,
        AppHelper helper,
        EmailService emailService,
        ILogger<AttendenceController> logger,
        IMemoryCache cache,
        IServiceProvider serviceProvider)
        : base(context, bccontext, httpContextAccessor, helper)
    {
        _db = context;
        _v = new AttendenceViewModel(context, httpContextAccessor, helper);
        _emailService = emailService;
        _logger = logger;
        _cache = cache;
        _serviceProvider = serviceProvider;

        _v.Page.Active = "attendence";

        // Register email template if it doesn't exist
        RegisterAttendanceWarningTemplate();
    }
    
    private void RegisterAttendanceWarningTemplate()
    {
        // This method is now simplified since EmailService directly checks for templates in the file system
        const string templateName = "attendance.warning";
        
        // We can optionally log that we're relying on a file system template
        try {
            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "Modules", "Shared", "Views", "EmailTemplates", $"{templateName}.cshtml");
            if (!System.IO.File.Exists(templatePath))
            {
                // Log a warning if the template file doesn't exist
                _logger.LogWarning($"Email template file '{templateName}.cshtml' not found in EmailTemplates directory");
            }
        }
        catch (Exception ex) {
            _logger.LogError(ex, $"Error checking for email template: {ex.Message}");
        }
    }

    public IActionResult Index()
    {
        return View(_v);
    }


    public IActionResult Schedules()
    {
        return View(_v);
    }

    [HttpGet("DailyReport")]
    public IActionResult DailyReport(DateTime? date)
    {
        date ??= DateTime.Today;
        
        _v.SelectedDate = date.Value;
        _v.DailyAttendance = _db.EmpWorkHours
            .Where(w => w.Day.Date == date.Value.Date)
            .OrderBy(w => w.EmpNo)
            .ToList();
            
        // Get the list of all employees
        _v.VempDtls = _db.VempDtls.Where(v => v.DesgnHierLevel > 4).ToList();
        
        return View(_v);
    }
    
    [HttpGet("MonthlyNonAttendanceReport")]
    public IActionResult MonthlyNonAttendanceReport(int? month, int? year)
    {
        month ??= DateTime.Today.Month;
        year ??= DateTime.Today.Year;
        
        _v.SelectedMonth = month.Value;
        _v.SelectedYear = year.Value;
        
        // Get start and end date of the selected month
        var startDate = new DateTime(year.Value, month.Value, 1);
        var endDate = startDate.AddMonths(1).AddDays(-1);
        
        // Get all employees
        var allEmployees = _db.VempDtls.Where(v => v.DesgnHierLevel > 4).ToList();
        
        // Get all attendance records for the month
        var attendanceRecords = _db.EmpWorkHours
            .Where(w => w.Day.Date >= startDate && w.Day.Date <= endDate)
            .ToList();
        
        // Group attendance records by date
        var attendanceByDate = new Dictionary<DateTime, List<int>>();
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var attendedEmployees = attendanceRecords
                .Where(a => a.Day.Date == date.Date)
                .Select(a => a.EmpNo)
                .ToList();
                
            attendanceByDate[date] = attendedEmployees;
        }
        
        _v.AttendanceByDate = attendanceByDate;
        _v.VempDtls = allEmployees;
        
        return View(_v);
    }
    
    [HttpGet("My")]
    public IActionResult MyAttendance(int? month, int? year)
    {
        month ??= DateTime.Today.Month;
        year ??= DateTime.Today.Year;
        
        _v.SelectedMonth = month.Value;
        _v.SelectedYear = year.Value;
        _v.Page.Active="";
        
        // Get the logged-in employee
        var employeeNo = Auth().EmpNo;
        
        // Get start and end date of the selected month
        var startDate = new DateTime(year.Value, month.Value, 1);
        var endDate = startDate.AddMonths(1).AddDays(-1);
        
        // Get attendance records for the employee during the selected month
        _v.DailyAttendance = _db.EmpWorkHours
            .Where(w => w.EmpNo == employeeNo && w.Day.Date >= startDate && w.Day.Date <= endDate)
            .OrderBy(w => w.Day)
            .ToList();
            
        // Get employee details
        _v.Employee = _db.VempDtls.FirstOrDefault(e => e.EmpNo == employeeNo);
        
        // Get excuses for the month
        _v.MonthlyExcuses = _db.Absents
            .Where(e => e.EmpNo == employeeNo && e.OrderDate >= startDate && e.OrderDate <= endDate)
            .OrderBy(e => e.OrderDate)
            .ToList();
            
        // Get leave information for the month
        _v.LeaveApplications = _db.TleaveAplTxs
            .Where(l => l.EmpNo == employeeNo && 
                  l.CancelFlag == 0 && // Not canceled
                  (l.LeaveStartDate >= startDate && l.LeaveStartDate <= endDate || 
                   l.LeaveEndDate >= startDate && l.LeaveEndDate <= endDate ||
                   l.LeaveStartDate <= startDate && l.LeaveEndDate >= endDate))
            .Include(l => l.TleaveCode) // Include the leave code description
            .ToList();
            
        // Get all leave codes for reference
        _v.LeaveCodes = _db.TleaveCodes.ToList();
            
        // Calculate total excuse duration for the month
        _v.TotalExcuseDuration = 0;
        foreach (var excuse in _v.MonthlyExcuses)
        {
            if (excuse.TimeFrom.HasValue && excuse.TimeTo.HasValue)
            {
                var duration = (float)(excuse.TimeTo.Value - excuse.TimeFrom.Value).TotalHours;
                _v.TotalExcuseDuration += duration;
            }
        }
        
        return View(_v);
    }
    
    [HttpGet("AttendanceReport")]
    public IActionResult AttendanceReport(DateTime? from, DateTime? to, int Dg = 0, int Dept = -1, int Section = 0, int status = 0)
    {
        // Set default date filters if not provided
        from ??= DateTime.Today.AddDays(-30); // Default to last 30 days
        to ??= DateTime.Today;
        
        // Store filter values in viewmodel
        _v.FromDate = from.Value;
        _v.ToDate = to.Value;
        _v.SelectedDg = Dg;
        _v.SelectedDept = Dept;
        _v.SelectedSection = Section;
        _v.StatusFilter = (AttendenceViewModel.AttendanceStatus)status;
        
        // Get all employees based on organizational filters
        var employeeQuery = _db.VempDtls.Where(v => v.DesgnHierLevel > 4 && !new List<int> { 2, 3, 4, 12, 13 }.Contains(v.DesgType.Value));
        
        // Apply organizational filters
        if (Dg > 0)
        {
            employeeQuery = employeeQuery.Where(e => e.DgCode == Dg);
        }
        
        if (Dept > -1)
        {
            employeeQuery = employeeQuery.Where(e => e.DeptCode == Dept);
        }

        if (Section > 0)
        {
            employeeQuery = employeeQuery.Where(e => e.SectionCode == Section);
        }
        
        // The SectionCode filter needs special handling since VempDtl doesn't have it
        List<int> employeeNumbers = new List<int>();
        employeeNumbers = employeeQuery.Where(e => e.EmpNo.HasValue).Select(e => e.EmpNo.Value).ToList();
        
        // Get filtered employees
        var employees = employeeQuery.ToList();
        _v.VempDtls = employees;
        
        // Get all DGs, Departments, and Sections for filter dropdowns
        _v.Dgs = _db.TdgCodes.Where(d => d.DgCode > 0  ).OrderBy(d => d.DgDespA).ToList();
        _v.Departments = _db.TDeptCode.Where(d => d.DeptCode > -1).OrderBy(d => d.DeptDespA).ToList();
        _v.Sections = _db.TsectionCodes.Where(s => s.SectionCode > 0).OrderBy(s => s.SectionDespA).ToList();
        
        // Get attendance records for these employees in the date range
        _v.DailyAttendance = _db.EmpWorkHours
            .Where(w => employeeNumbers.Contains(w.EmpNo) && 
                  w.Day.Date >= from.Value.Date && 
                  w.Day.Date <= to.Value.Date)
            .OrderBy(w => w.Day)
            .ThenBy(w => w.EmpNo)
            .ToList();
            
        // Get previously sent warnings for these employees in the date range
        _v.AttendanceWarnings = _db.AttendanceWarnings
            .Where(w => employeeNumbers.Contains(w.EmpNo) && 
                  w.AttendanceDate.Date >= from.Value.Date && 
                  w.AttendanceDate.Date <= to.Value.Date)
            .ToList();
        

    
        
        // Get leave applications for these employees in the date range
        _v.AllLeaveApplications = _db.TleaveAplTxs
            .Where(l => employeeNumbers.Contains(l.EmpNo) && 
                  l.CancelFlag != 1 && // Not canceled
                  l.ReqStat != 3 && // not rejected
                  (l.LeaveStartDate >= from.Value.Date && l.LeaveStartDate <= to.Value.Date || 
                   l.LeaveEndDate >= from.Value.Date && l.LeaveEndDate <= to.Value.Date ||
                   l.LeaveStartDate <= from.Value.Date && l.LeaveEndDate >= to.Value.Date))
            .Include(l => l.TleaveCode) // Include the leave code description
            .ToList();

        var tempTrgHist = _db.TempTrgHist
            .Where(t =>  employeeNumbers.Contains(t.EmpNo) && 
                  t.CourseStartDate >= from.Value.Date && t.CourseEndDate <= to.Value.Date &&
                  (t.CourseStartDate >= from.Value.Date && t.CourseStartDate <= to.Value.Date || 
                   t.CourseEndDate >= from.Value.Date && t.CourseEndDate <= to.Value.Date ||
                   t.CourseStartDate <= from.Value.Date && t.CourseEndDate >= to.Value.Date))
            .OrderBy(t => t.CourseStartDate)
            .ToList();

        _v.AllTraining = tempTrgHist;
        
        // Get excuse requests for these employees in the date range
        _v.AllExcuses = _db.Absents
            .Where(e => e.EmpNo.HasValue && employeeNumbers.Contains(e.EmpNo.Value) && 
                  e.OrderDate.HasValue && e.OrderDate.Value.Date >= from.Value.Date && e.OrderDate.Value.Date <= to.Value.Date)
            .OrderBy(e => e.OrderDate)
            .ToList();
            
        // Get holidays in the date range
        var holidays = _db.Tholidays
            .Where(h => h.Holiday.HasValue && 
                   h.Holiday.Value.Date >= from.Value.Date && 
                   h.Holiday.Value.Date <= to.Value.Date)
            .Select(h => h.Holiday.Value.Date)
            .ToList();
        
        _v.Holidays = holidays;
        
        // Calculate statistics
        _v.Statistics = new Dictionary<string, int>();
        
        // Count total work days in the period (excluding weekends and holidays)
        int totalWorkDays = 0;
        for (var date = from.Value.Date; date <= to.Value.Date; date = date.AddDays(1))
        {
            if (date.DayOfWeek != DayOfWeek.Friday && 
                date.DayOfWeek != DayOfWeek.Saturday && 
                !holidays.Contains(date.Date))
            {
                totalWorkDays++;
            }
        }
        _v.Statistics["TotalWorkDays"] = totalWorkDays;
        
        // Prepare statistics by status
        _v.Statistics["CompleteAttendance"] = 0;
        _v.Statistics["IncompleteAttendance"] = 0;
        _v.Statistics["Absent"] = 0;
        _v.Statistics["Excused"] = 0;
        _v.Statistics["Training"] = 0;
        _v.Statistics["Leave"] = 0;
        // Count records by status (for each employee and each workday)
        foreach (var emp in employees)
        {
            if (!emp.EmpNo.HasValue) continue;
            
            for (var date = from.Value.Date; date <= to.Value.Date; date = date.AddDays(1))
            {
                if (_v.IsWeekend(date) || _v.IsHoliday(date)) continue;
                
                // Skip if employee is on leave
                if (_v.GetAllLeaveForDate(emp.EmpNo.Value, date) != null) {
                    _v.Statistics["Leave"]++;
                    continue;
                }

                if (_v.GetAllTrainingForDate(emp.EmpNo.Value, date) != null) {
                    _v.Statistics["Training"]++;
                    continue;
                }
                
                var attendanceRecord = _v.DailyAttendance.FirstOrDefault(a => a.EmpNo == emp.EmpNo && a.Day.Date == date.Date);
                var attendanceStatus = _v.GetAttendanceStatus(emp.EmpNo.Value, date, attendanceRecord);
                
                // Update counter for the appropriate status
                switch (attendanceStatus)
                {
                    case AttendenceViewModel.AttendanceStatus.Complete:
                        _v.Statistics["CompleteAttendance"]++;
                        break;
                    case AttendenceViewModel.AttendanceStatus.Incomplete:
                        _v.Statistics["IncompleteAttendance"]++;
                        break;
                    case AttendenceViewModel.AttendanceStatus.Absent:
                        _v.Statistics["Absent"]++;
                        break;
           
                }
            }
        }
        
        // Count employees with attendance records
        var uniqueAttendanceDays = _v.DailyAttendance
            .GroupBy(a => new { a.EmpNo, a.Day.Date })
            .Select(g => g.Key)
            .ToList();
        
        _v.Statistics["TotalAttendanceRecords"] = uniqueAttendanceDays.Count;
        
        // Count employees with leave
        var uniqueLeaveEmployees = _v.AllLeaveApplications
            .Select(l => l.EmpNo)
            .Distinct()
            .Count();
        
        _v.Statistics["EmployeesOnLeave"] = uniqueLeaveEmployees;
        
        // Count employees with excuses
        var uniqueExcuseEmployees = _v.AllExcuses
            .Where(e => e.EmpNo.HasValue)
            .Select(e => e.EmpNo.Value)
            .Distinct()
            .Count();
        
        _v.Statistics["EmployeesWithExcuses"] = uniqueExcuseEmployees;
        
        // Expected attendance (employees × work days)
        _v.Statistics["ExpectedAttendance"] = employees.Count * totalWorkDays;
        
        // Attendance rate (actual/expected)
        if (_v.Statistics["ExpectedAttendance"] > 0)
        {
            double attendancePercentage = (double)_v.Statistics["TotalAttendanceRecords"] / _v.Statistics["ExpectedAttendance"] * 100;
            _v.AttendancePercentage = Math.Round(attendancePercentage, 2);
        }
        else
        {
            _v.AttendancePercentage = 0;
        }

    
        
        return View(_v);
    }
    
    [HttpPost("SendWarningEmail")]
    public async Task<IActionResult> SendWarningEmail(int warningEmpNo, string warningDate, string warningMessage)
    {
        try
        {
            // Get employee information
            var employee = _db.VempDtls.FirstOrDefault(e => e.EmpNo == warningEmpNo);
            if (employee == null)
            {
                return Json(new { success = false, message = "Employee not found" });
            }
            
            string emailAddress = employee.EmailId + "@gsc.local";

            // Generate email address
            //string emailAddress = $"{warningEmpNo}@mail.gsc.local";
            
            // Parse date for formatting
            DateTime parsedDate;
            if (!DateTime.TryParse(warningDate, out parsedDate))
            {
                parsedDate = DateTime.Now;
            }
            
            // Create variables dictionary for email template
            var variables = new Dictionary<string, string>
            {
                { "EmpNameA", employee.EmpNameA },
                { "EmpNo", employee.EmpNo.ToString() },
                { "Date", parsedDate.ToString("yyyy-MM-dd") },
                { "DgDespA", employee.DgDespA ?? "المديرية" },
                { "DeptDespA", employee.DeptDespA ?? "القسم" },
            };
            

            var message = _emailService.BindVariables(warningMessage, variables);

            // Use template to send email - the EmailService will automatically look for the template file
            await _emailService.SendEmailAsync(
                emailAddress,
                "تنبيه بشأن الحضور والانصراف",
                message
            );
            
            // Record that a warning has been sent to this employee for this date
            var warning = new AttendanceWarning
            {
                EmpNo = warningEmpNo,
                AttendanceDate = parsedDate
            };
            
            // Check if a warning already exists for this employee and date
            var existingWarning = _db.AttendanceWarnings
                .FirstOrDefault(w => w.EmpNo == warningEmpNo && w.AttendanceDate.Date == parsedDate.Date);

            if (existingWarning == null)
            {
                // Add new warning record
                _db.AttendanceWarnings.Add(warning);
                await _db.SaveChangesAsync();

                var employeeWarning = new EmployeeWarning
                {
                    EmpNo = warningEmpNo,
                    Title = "تنبيه بشأن الحضور والانصراف",
                    Body = warningMessage,
                    WarningDate = parsedDate
                };
                _db.EmployeeWarnings.Add(employeeWarning);
                await _db.SaveChangesAsync();

            }
            
            // Log the email sending
            _logger.LogInformation($"Warning email sent to {emailAddress} regarding attendance on {warningDate}");
            
            return Json(new { 
                success = true,
                message = new List<string> { $"تم إرسال التنبيه إلى {emailAddress} بنجاح" },
                action="$('#warning-button-" + warningEmpNo + "-" + warningDate + "').html('<span class=\"badge bg-success\"><i class=\"fas fa-check\"></i> تم إرسال التنبيه</span>'); $('#modal-warning').modal('hide');" });
        }
        catch (Exception ex)
        {
            // Log the error
            _logger.LogError(ex, $"Error sending warning email: {ex.Message}");
            return Json(new { success = false, message = new List<string> { $"خطأ في إرسال البريد الإلكتروني: {ex.Message}" } });
        }
    }

    /// <summary>
    /// Sends bulk warning emails to employees based on attendance status criteria
    /// [doc-ref:attendance-reports.md#bulk-warning-system]
    /// </summary>
    [HttpPost("SendBulkWarningEmails")]
    public async Task<IActionResult> SendBulkWarningEmails(DateTime fromDate, DateTime toDate,
        int dgCode = 0, int deptCode = -1, int sectionCode = 0, int statusFilter = 3, string warningMessage = "")
    {
        try
        {
            // Generate unique job ID for tracking
            var jobId = Guid.NewGuid().ToString();

            // Initialize job status in cache
            var jobStatus = new BulkWarningJobStatus
            {
                JobId = jobId,
                Status = "Initializing",
                Progress = 0,
                TotalEmployees = 0,
                ProcessedEmployees = 0,
                SuccessfulEmails = 0,
                FailedEmails = 0,
                StartTime = DateTime.Now,
                Errors = new List<string>()
            };

            _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));

            // Start background processing
            Task.Run(async () => await ProcessBulkWarnings(jobId, fromDate, toDate,
                dgCode, deptCode, sectionCode, statusFilter, warningMessage));

            _logger.LogInformation("Bulk warning job started with ID: {JobId}", jobId);

            return Json(new {
                success = true,
                jobId = jobId,
                message = "تم بدء عملية إرسال التنبيهات في الخلفية. يمكنك متابعة التقدم من خلال النافذة المنبثقة."
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting bulk warning job: {Message}", ex.Message);
            return Json(new {
                success = false,
                message = $"خطأ في بدء عملية الإرسال: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Gets the progress status of a bulk warning job
    /// </summary>
    [HttpGet("GetBulkWarningProgress/{jobId}")]
    public IActionResult GetBulkWarningProgress(string jobId)
    {
        var status = _cache.Get<BulkWarningJobStatus>($"bulk_warning_job_{jobId}");

        if (status == null)
        {
            return Json(new BulkWarningJobStatus { Status = "NotFound" });
        }

        return Json(status);
    }

    /// <summary>
    /// Background method to process bulk warning emails using two-phase approach
    /// Phase 1: Insert all warning records into database
    /// Phase 2: Process email sending from database records
    /// </summary>
    private async Task ProcessBulkWarnings(string jobId, DateTime fromDate, DateTime toDate,
        int dgCode, int deptCode, int sectionCode, int statusFilter, string warningMessage)
    {
        var jobStatus = _cache.Get<BulkWarningJobStatus>($"bulk_warning_job_{jobId}");
        if (jobStatus == null) return;

        // Create a new scope for the background task to get fresh DbContext
        using var scope = _serviceProvider.CreateScope();
        var scopedDb = scope.ServiceProvider.GetRequiredService<hrmsContext>();
        var scopedEmailService = scope.ServiceProvider.GetRequiredService<EmailService>();
        var scopedHelper = scope.ServiceProvider.GetRequiredService<AppHelper>();

        try
        {
            // PHASE 1: Database Insert Phase
            jobStatus.Status = "DatabaseInsert";
            jobStatus.Phase = "Phase 1: Database Insert";
            _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));

            var emailRecords = await Phase1_InsertWarningRecords(jobId, fromDate, toDate,
                dgCode, deptCode, sectionCode, statusFilter, warningMessage, scopedDb, scopedHelper);

            if (emailRecords.Count == 0)
            {
                jobStatus.Status = "Completed";
                jobStatus.Phase = "No records to process";
                jobStatus.Progress = 100;
                jobStatus.EndTime = DateTime.Now;
                _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));
                return;
            }

            // Update status after Phase 1 completion
            jobStatus.DatabaseRecordsInserted = emailRecords.Count;
            jobStatus.TotalEmployees = emailRecords.Count;
            jobStatus.PendingEmails = emailRecords.Count;
            jobStatus.DatabasePhaseCompleted = DateTime.Now;
            jobStatus.Status = "EmailProcessing";
            jobStatus.Phase = "Phase 2: Email Processing";
            jobStatus.Progress = 25; // 25% after database phase
            _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));

            // PHASE 2: Email Processing Phase
            await Phase2_ProcessEmailSending(jobId, emailRecords, scopedEmailService);

            // Mark job as completed
            jobStatus = _cache.Get<BulkWarningJobStatus>($"bulk_warning_job_{jobId}");
            if (jobStatus != null)
            {
                jobStatus.Status = "Completed";
                jobStatus.Phase = "Completed";
                jobStatus.EndTime = DateTime.Now;
                jobStatus.Progress = 100;
                _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));
            }

            _logger.LogInformation("Bulk warning job {JobId} completed successfully. Database records: {DatabaseRecords}, Success: {SuccessCount}, Failed: {FailedCount}",
                jobId, jobStatus?.DatabaseRecordsInserted ?? 0, jobStatus?.SuccessfulEmails ?? 0, jobStatus?.FailedEmails ?? 0);

            // Get employees based on filters (similar to AttendanceReport logic)
            var employeeQuery = scopedDb.VempDtls.Where(v => v.DesgnHierLevel > 4 &&
                !new List<int> { 2, 3, 4, 12, 13 }.Contains(v.DesgType.Value));

            // Apply organizational filters
            if (dgCode > 0)
                employeeQuery = employeeQuery.Where(e => e.DgCode == dgCode);
            if (deptCode > -1)
                employeeQuery = employeeQuery.Where(e => e.DeptCode == deptCode);
            if (sectionCode > 0)
                employeeQuery = employeeQuery.Where(e => e.SectionCode == sectionCode);

            var employeeNumbers = employeeQuery.Where(e => e.EmpNo.HasValue)
                .Select(e => e.EmpNo.Value).ToList();

            // Get attendance records for the date range
            var attendanceRecords = scopedDb.EmpWorkHours
                .Where(w => employeeNumbers.Contains(w.EmpNo) &&
                      w.Day.Date >= fromDate.Date &&
                      w.Day.Date <= toDate.Date)
                .ToList();

            // Get existing warnings to avoid duplicates
            var existingWarnings = scopedDb.AttendanceWarnings
                .Where(w => employeeNumbers.Contains(w.EmpNo) &&
                      w.AttendanceDate.Date >= fromDate.Date &&
                      w.AttendanceDate.Date <= toDate.Date)
                .ToList();

            // Create a scoped AttendenceViewModel for status evaluation
            var scopedHttpContextAccessor = scope.ServiceProvider.GetRequiredService<IHttpContextAccessor>();
            var scopedViewModel = new AttendenceViewModel(scopedDb, scopedHttpContextAccessor, scopedHelper);

            // Process employees based on status filter
            var employeesToWarn = new List<(int EmpNo, DateTime Date, string Reason)>();

            foreach (var empNo in employeeNumbers)
            {
                var employee = employeeQuery.FirstOrDefault(e => e.EmpNo == empNo);
                if (employee == null) continue;

                for (var date = fromDate.Date; date <= toDate.Date; date = date.AddDays(1))
                {
                    // Skip weekends and holidays
                    if (date.DayOfWeek == DayOfWeek.Friday || date.DayOfWeek == DayOfWeek.Saturday)
                        continue;

                    // Check if warning already sent
                    if (existingWarnings.Any(w => w.EmpNo == empNo && w.AttendanceDate.Date == date))
                        continue;

                    var attendanceRecord = attendanceRecords.FirstOrDefault(a => a.EmpNo == empNo && a.Day.Date == date);
                    var attendanceStatus = scopedViewModel.GetAttendanceStatus(empNo, date, attendanceRecord);

                    // Apply status filter
                    bool shouldWarn = statusFilter switch
                    {
                        1 => attendanceStatus == AttendenceViewModel.AttendanceStatus.Complete, // Complete attendance
                        2 => attendanceStatus == AttendenceViewModel.AttendanceStatus.Incomplete, // Incomplete attendance
                        3 => attendanceStatus == AttendenceViewModel.AttendanceStatus.Absent, // Absent
                        _ => attendanceStatus == AttendenceViewModel.AttendanceStatus.Absent
                    };

                    if (shouldWarn)
                    {
                        string reason = attendanceStatus switch
                        {
                            AttendenceViewModel.AttendanceStatus.Absent => "غائب",
                            AttendenceViewModel.AttendanceStatus.Incomplete => "دوام ناقص",
                            AttendenceViewModel.AttendanceStatus.Complete => "دوام كامل",
                            _ => "غير محدد"
                        };

                        employeesToWarn.Add((empNo, date, reason));
                    }
                }
            }

            jobStatus.TotalEmployees = employeesToWarn.Count;
            _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));

            // Process in batches to avoid overwhelming the system
            const int batchSize = 50;
            var batches = employeesToWarn.Select((item, index) => new { item, index })
                .GroupBy(x => x.index / batchSize)
                .Select(g => g.Select(x => x.item).ToList())
                .ToList();

            foreach (var batch in batches)
            {
                await ProcessWarningBatch(batch, jobId, warningMessage, scopedDb, scopedEmailService);

                // Small delay between batches to prevent server overload
                await Task.Delay(100);
            }

            // Mark job as completed
            jobStatus = _cache.Get<BulkWarningJobStatus>($"bulk_warning_job_{jobId}");
            if (jobStatus != null)
            {
                jobStatus.Status = "Completed";
                jobStatus.EndTime = DateTime.Now;
                jobStatus.Progress = 100;
                _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));
            }

            _logger.LogInformation("Bulk warning job {JobId} completed successfully. Processed: {ProcessedCount}, Success: {SuccessCount}, Failed: {FailedCount}",
                jobId, jobStatus?.ProcessedEmployees ?? 0, jobStatus?.SuccessfulEmails ?? 0, jobStatus?.FailedEmails ?? 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in bulk warning job {JobId}: {Message}", jobId, ex.Message);

            jobStatus = _cache.Get<BulkWarningJobStatus>($"bulk_warning_job_{jobId}");
            if (jobStatus != null)
            {
                jobStatus.Status = "Failed";
                jobStatus.EndTime = DateTime.Now;
                jobStatus.Errors.Add($"خطأ عام: {ex.Message}");
                _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));
            }
        }
    }

    /// <summary>
    /// Phase 1: Insert all warning records into database in a single transaction
    /// </summary>
    private async Task<List<BulkWarningEmail>> Phase1_InsertWarningRecords(string jobId, DateTime fromDate, DateTime toDate,
        int dgCode, int deptCode, int sectionCode, int statusFilter, string warningMessage,
        hrmsContext db, AppHelper helper)
    {
        var emailRecords = new List<BulkWarningEmail>();
        var jobStatus = _cache.Get<BulkWarningJobStatus>($"bulk_warning_job_{jobId}");
        if (jobStatus == null) return emailRecords;

        try
        {
            // Get employees based on filters (similar to AttendanceReport logic)
            var employeeQuery = db.VempDtls.Where(v => v.DesgnHierLevel > 4 &&
                !new List<int> { 2, 3, 4, 12, 13 }.Contains(v.DesgType.Value));

            // Apply organizational filters
            if (dgCode > 0)
                employeeQuery = employeeQuery.Where(e => e.DgCode == dgCode);
            if (deptCode > -1)
                employeeQuery = employeeQuery.Where(e => e.DeptCode == deptCode);
            if (sectionCode > 0)
                employeeQuery = employeeQuery.Where(e => e.SectionCode == sectionCode);

            var employeeNumbers = employeeQuery.Where(e => e.EmpNo.HasValue)
                .Select(e => e.EmpNo.Value).ToList();

            // Get attendance records for the date range
            var attendanceRecords = db.EmpWorkHours
                .Where(w => employeeNumbers.Contains(w.EmpNo) &&
                      w.Day.Date >= fromDate.Date &&
                      w.Day.Date <= toDate.Date)
                .ToList();

            // Get existing warnings to avoid duplicates
            var existingWarnings = db.AttendanceWarnings
                .Where(w => employeeNumbers.Contains(w.EmpNo) &&
                      w.AttendanceDate.Date >= fromDate.Date &&
                      w.AttendanceDate.Date <= toDate.Date)
                .ToList();

            // Create a scoped AttendenceViewModel for status evaluation
            var scopedHttpContextAccessor = _serviceProvider.CreateScope().ServiceProvider.GetRequiredService<IHttpContextAccessor>();
            var scopedViewModel = new AttendenceViewModel(db, scopedHttpContextAccessor, helper);

            // Collect all warnings to insert
            var warningsToInsert = new List<AttendanceWarning>();
            var employeeWarningsToInsert = new List<EmployeeWarning>();

            foreach (var empNo in employeeNumbers)
            {
                var employee = employeeQuery.FirstOrDefault(e => e.EmpNo == empNo);
                if (employee == null) continue;

                for (var date = fromDate.Date; date <= toDate.Date; date = date.AddDays(1))
                {
                    // Skip weekends and holidays
                    if (date.DayOfWeek == DayOfWeek.Friday || date.DayOfWeek == DayOfWeek.Saturday)
                        continue;

                    // Check if warning already sent
                    if (existingWarnings.Any(w => w.EmpNo == empNo && w.AttendanceDate.Date == date))
                        continue;

                    var attendanceRecord = attendanceRecords.FirstOrDefault(a => a.EmpNo == empNo && a.Day.Date == date);
                    var attendanceStatus = scopedViewModel.GetAttendanceStatus(empNo, date, attendanceRecord);

                    // Apply status filter
                    bool shouldWarn = statusFilter switch
                    {
                        1 => attendanceStatus == AttendenceViewModel.AttendanceStatus.Complete,
                        2 => attendanceStatus == AttendenceViewModel.AttendanceStatus.Incomplete,
                        3 => attendanceStatus == AttendenceViewModel.AttendanceStatus.Absent,
                        _ => attendanceStatus == AttendenceViewModel.AttendanceStatus.Absent
                    };

                    if (shouldWarn)
                    {
                        string reason = attendanceStatus switch
                        {
                            AttendenceViewModel.AttendanceStatus.Absent => "غائب",
                            AttendenceViewModel.AttendanceStatus.Incomplete => "دوام ناقص",
                            AttendenceViewModel.AttendanceStatus.Complete => "دوام كامل",
                            _ => "غير محدد"
                        };

                        string emailAddress = employee.EmailId + "@gsc.local";

                        // Create variables dictionary for email template
                        var variables = new Dictionary<string, string>
                        {
                            { "EmpNameA", employee.EmpNameA },
                            { "EmpNo", employee.EmpNo.ToString() },
                            { "Date", date.ToString("yyyy-MM-dd") },
                            { "DgDespA", employee.DgDespA ?? "المديرية" },
                            { "DeptDespA", employee.DeptDespA ?? "القسم" },
                            { "Reason", reason }
                        };

                        var emailService = _serviceProvider.CreateScope().ServiceProvider.GetRequiredService<EmailService>();
                        var processedMessage = emailService.BindVariables(warningMessage, variables);

                        // Add to database insert collections
                        warningsToInsert.Add(new AttendanceWarning
                        {
                            EmpNo = empNo,
                            AttendanceDate = date
                        });

                        employeeWarningsToInsert.Add(new EmployeeWarning
                        {
                            EmpNo = empNo,
                            Title = "تنبيه بشأن الحضور والانصراف",
                            Body = processedMessage,
                            WarningDate = date,
                            Timestamp = DateTime.Now
                        });

                        // Add to email processing list
                        emailRecords.Add(new BulkWarningEmail
                        {
                            JobId = jobId,
                            EmpNo = empNo,
                            WarningDate = date,
                            EmailAddress = emailAddress,
                            WarningMessage = processedMessage,
                            Reason = reason,
                            EmailSent = false,
                            CreatedAt = DateTime.Now
                        });
                    }
                }
            }

            // Batch insert all records in a single transaction
            using var transaction = await db.Database.BeginTransactionAsync();
            try
            {
                if (warningsToInsert.Count > 0)
                {
                    db.AttendanceWarnings.AddRange(warningsToInsert);
                    await db.SaveChangesAsync();
                }

                if (employeeWarningsToInsert.Count > 0)
                {
                    db.EmployeeWarnings.AddRange(employeeWarningsToInsert);
                    await db.SaveChangesAsync();
                }

                await transaction.CommitAsync();

                _logger.LogInformation("Phase 1 completed for job {JobId}: {RecordCount} warning records inserted",
                    jobId, warningsToInsert.Count);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error in Phase 1 database insert for job {JobId}: {Message}", jobId, ex.Message);
                throw;
            }

            return emailRecords;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Phase 1 for job {JobId}: {Message}", jobId, ex.Message);

            if (jobStatus != null)
            {
                jobStatus.Status = "Failed";
                jobStatus.Phase = "Phase 1 Failed";
                jobStatus.Errors.Add($"خطأ في مرحلة إدراج البيانات: {ex.Message}");
                _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));
            }

            throw;
        }
    }

    /// <summary>
    /// Phase 2: Process email sending from database records with retry capability
    /// </summary>
    private async Task Phase2_ProcessEmailSending(string jobId, List<BulkWarningEmail> emailRecords, EmailService emailService)
    {
        var jobStatus = _cache.Get<BulkWarningJobStatus>($"bulk_warning_job_{jobId}");
        if (jobStatus == null) return;

        try
        {
            // Process emails in batches to avoid overwhelming the email service
            const int batchSize = 20; // Smaller batch size for email processing
            var batches = emailRecords.Select((item, index) => new { item, index })
                .GroupBy(x => x.index / batchSize)
                .Select(g => g.Select(x => x.item).ToList())
                .ToList();

            int totalProcessed = 0;
            int successCount = 0;
            int failureCount = 0;

            foreach (var batch in batches)
            {
                foreach (var emailRecord in batch)
                {
                    try
                    {
                        // Send email
                        await emailService.SendEmailAsync(
                            emailRecord.EmailAddress,
                            "تنبيه بشأن الحضور والانصراف",
                            emailRecord.WarningMessage
                        );

                        // Mark as sent (in memory tracking)
                        emailRecord.EmailSent = true;
                        emailRecord.EmailSentAt = DateTime.Now;
                        successCount++;

                        _logger.LogDebug("Email sent successfully to {EmailAddress} for employee {EmpNo} on {Date}",
                            emailRecord.EmailAddress, emailRecord.EmpNo, emailRecord.WarningDate);
                    }
                    catch (Exception ex)
                    {
                        // Mark as failed (in memory tracking)
                        emailRecord.EmailSent = false;
                        emailRecord.EmailError = ex.Message;
                        emailRecord.RetryCount++;
                        failureCount++;

                        _logger.LogError(ex, "Failed to send email to {EmailAddress} for employee {EmpNo} on {Date}: {Message}",
                            emailRecord.EmailAddress, emailRecord.EmpNo, emailRecord.WarningDate, ex.Message);

                        // Add to job status errors
                        if (jobStatus != null)
                        {
                            jobStatus.Errors.Add($"فشل إرسال التنبيه للموظف {emailRecord.EmpNo} ({emailRecord.EmailAddress}): {ex.Message}");
                        }
                    }

                    totalProcessed++;

                    // Update progress
                    if (jobStatus != null)
                    {
                        jobStatus.ProcessedEmployees = totalProcessed;
                        jobStatus.SuccessfulEmails = successCount;
                        jobStatus.FailedEmails = failureCount;
                        jobStatus.PendingEmails = emailRecords.Count - totalProcessed;

                        // Progress: 25% for database phase + 75% for email phase
                        var emailProgress = (double)totalProcessed / emailRecords.Count * 75;
                        jobStatus.Progress = (int)(25 + emailProgress);

                        _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));
                    }
                }

                // Small delay between batches to prevent overwhelming the email server
                await Task.Delay(200);
            }

            _logger.LogInformation("Phase 2 completed for job {JobId}: {SuccessCount} emails sent successfully, {FailureCount} failed",
                jobId, successCount, failureCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Phase 2 for job {JobId}: {Message}", jobId, ex.Message);

            if (jobStatus != null)
            {
                jobStatus.Status = "Failed";
                jobStatus.Phase = "Phase 2 Failed";
                jobStatus.Errors.Add($"خطأ في مرحلة إرسال البريد الإلكتروني: {ex.Message}");
                _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));
            }

            throw;
        }
    }

    /// <summary>
    /// Legacy method - kept for compatibility but not used in two-phase approach
    /// </summary>
    private async Task ProcessWarningBatch(List<(int EmpNo, DateTime Date, string Reason)> batch,
        string jobId, string warningMessage, hrmsContext db, EmailService emailService)
    {
        var jobStatus = _cache.Get<BulkWarningJobStatus>($"bulk_warning_job_{jobId}");
        if (jobStatus == null) return;

        foreach (var (empNo, date, reason) in batch)
        {
            try
            {
                // Get employee information
                var employee = db.VempDtls.FirstOrDefault(e => e.EmpNo == empNo);
                if (employee == null)
                {
                    jobStatus.FailedEmails++;
                    jobStatus.Errors.Add($"لم يتم العثور على الموظف رقم {empNo}");
                    continue;
                }

                string emailAddress = employee.EmailId + "@gsc.local";

                // Create variables dictionary for email template
                var variables = new Dictionary<string, string>
                {
                    { "EmpNameA", employee.EmpNameA },
                    { "EmpNo", employee.EmpNo.ToString() },
                    { "Date", date.ToString("yyyy-MM-dd") },
                    { "DgDespA", employee.DgDespA ?? "المديرية" },
                    { "DeptDespA", employee.DeptDespA ?? "القسم" },
                    { "Reason", reason }
                };

                var message = emailService.BindVariables(warningMessage, variables);

                // Send email
                await emailService.SendEmailAsync(
                    emailAddress,
                    "تنبيه بشأن الحضور والانصراف",
                    message
                );

                // Record warning in database
                var warning = new AttendanceWarning
                {
                    EmpNo = empNo,
                    AttendanceDate = date
                };

                var existingWarning = db.AttendanceWarnings
                    .FirstOrDefault(w => w.EmpNo == empNo && w.AttendanceDate.Date == date.Date);

                if (existingWarning == null)
                {
                    db.AttendanceWarnings.Add(warning);

                    var employeeWarning = new EmployeeWarning
                    {
                        EmpNo = empNo,
                        Title = "تنبيه بشأن الحضور والانصراف",
                        Body = message,
                        WarningDate = date,
                        Timestamp = DateTime.Now
                    };
                    db.EmployeeWarnings.Add(employeeWarning);

                    await db.SaveChangesAsync();
                }

                jobStatus.SuccessfulEmails++;
                _logger.LogDebug("Warning email sent to {EmailAddress} for date {Date}", emailAddress, date);
            }
            catch (Exception ex)
            {
                jobStatus.FailedEmails++;
                jobStatus.Errors.Add($"فشل إرسال التنبيه للموظف {empNo} بتاريخ {date:yyyy-MM-dd}: {ex.Message}");
                _logger.LogError(ex, "Failed to send warning email to employee {EmpNo} for date {Date}", empNo, date);
            }
            finally
            {
                jobStatus.ProcessedEmployees++;
                if (jobStatus.TotalEmployees > 0)
                {
                    jobStatus.Progress = (int)((double)jobStatus.ProcessedEmployees / jobStatus.TotalEmployees * 100);
                }

                // Update cache with current progress
                _cache.Set($"bulk_warning_job_{jobId}", jobStatus, TimeSpan.FromHours(2));
            }
        }
    }

    /// <summary>
    /// Test endpoint to validate bulk warning functionality
    /// [doc-ref:attendance-reports.md#bulk-warning-system]
    /// </summary>
    [HttpGet("TestBulkWarningSetup")]
    public IActionResult TestBulkWarningSetup()
    {
        try
        {
            var testResults = new
            {
                EmailServiceAvailable = _emailService != null,
                CacheServiceAvailable = _cache != null,
                DatabaseConnectionActive = _db.Database.CanConnect(),
                AttendanceRecordsCount = _db.EmpWorkHours.Count(),
                EmployeesCount = _db.VempDtls.Where(v => v.DesgnHierLevel > 4).Count(),
                ExistingWarningsCount = _db.AttendanceWarnings.Count(),
                MemoryCacheWorking = TestMemoryCache(),
                EmailTemplatesAvailable = CheckEmailTemplates(),
                SystemReady = true
            };

            _logger.LogInformation("Bulk warning system test completed successfully");

            return Json(new {
                success = true,
                message = "نظام التنبيهات الجماعية جاهز للعمل",
                testResults = testResults
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing bulk warning setup: {Message}", ex.Message);
            return Json(new {
                success = false,
                message = $"خطأ في اختبار النظام: {ex.Message}"
            });
        }
    }

    private bool TestMemoryCache()
    {
        try
        {
            var testKey = "bulk_warning_test_" + Guid.NewGuid().ToString();
            var testValue = "test_value";

            _cache.Set(testKey, testValue, TimeSpan.FromMinutes(1));
            var retrievedValue = _cache.Get<string>(testKey);

            return retrievedValue == testValue;
        }
        catch
        {
            return false;
        }
    }

    private bool CheckEmailTemplates()
    {
        try
        {
            var template1 = _h.Settings.Get("email.template.attendence.warning1", "");
            var template2 = _h.Settings.Get("email.template.attendence.warning2", "");

            return !string.IsNullOrEmpty(template1) || !string.IsNullOrEmpty(template2);
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Status tracking class for bulk warning jobs
/// </summary>
public class BulkWarningJobStatus
{
    public string JobId { get; set; } = string.Empty;
    public string Status { get; set; } = "Pending"; // Pending, Initializing, DatabaseInsert, EmailProcessing, Completed, Failed
    public string Phase { get; set; } = ""; // Phase 1: Database Insert, Phase 2: Email Processing
    public int Progress { get; set; } = 0; // 0-100
    public int TotalEmployees { get; set; } = 0;
    public int ProcessedEmployees { get; set; } = 0;
    public int DatabaseRecordsInserted { get; set; } = 0;
    public int SuccessfulEmails { get; set; } = 0;
    public int FailedEmails { get; set; } = 0;
    public int PendingEmails { get; set; } = 0;
    public DateTime StartTime { get; set; } = DateTime.Now;
    public DateTime? DatabasePhaseCompleted { get; set; }
    public DateTime? EndTime { get; set; }
    public List<string> Errors { get; set; } = new List<string>();

    public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : DateTime.Now - StartTime;
}

/// <summary>
/// Tracks bulk warning email sending status for two-phase processing
/// </summary>
public class BulkWarningEmail
{
    public string JobId { get; set; } = string.Empty;
    public int EmpNo { get; set; }
    public DateTime WarningDate { get; set; }
    public string EmailAddress { get; set; } = string.Empty;
    public string WarningMessage { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public bool EmailSent { get; set; } = false;
    public DateTime? EmailSentAt { get; set; }
    public string EmailError { get; set; } = string.Empty;
    public int RetryCount { get; set; } = 0;
    public DateTime CreatedAt { get; set; } = DateTime.Now;
}
