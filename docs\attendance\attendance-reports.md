# Attendance Reports

## HR Attendance Reporting System
The system includes a comprehensive attendance report for HR managers:

1. **Report Features**
   - Access via the Employees/AttendanceReport endpoint with HR permission
   - Filter data by date range, department, and DG
   - Group metrics by department with detailed employee breakdowns
   
2. **Key Metrics Tracked**
   - Work hours: Expected vs. actual
   - Compliance rate: Percentage of expected hours worked
   - Late arrivals: Count and total hours (arriving after 8:30 AM)
   - Excuses: Count and total hours by type
   - Overtime hours and official missions count
   
3. **Visualization**
   - Bar charts for compliance rate by department
   - Comparative charts for expected vs. actual work hours
   - Department-level summary cards with key statistics
   - Color-coded compliance indicators (green > 90%, yellow > 75%, red < 75%)
   
4. **Implementation Notes**
   - Uses 7-hour workday standard for calculations (8:30 AM - 3:30 PM)
   - Excludes weekends (Friday and Saturday) from workday counts
   - Excludes official holidays defined in the Tholidays table
   - Only includes approved excuses or those under final approval
   - Highlights departments with lowest compliance rates first
   - Employees with DesgnHierLevel < 4 are automatically considered to have completed full work hours and are exempt from lateness tracking
   - Days where an employee has approved leave (ReqStat = 1, 4, or 6) are automatically considered as completed work hours with no lateness tracking
   - Employees receive full work hours (7 hours) for days with approved leave or official holidays, even if no attendance record exists

## Attendance Management Reports
The system provides dedicated attendance and absence reporting features:

1. **Daily Attendance Report**
   - Access via the Attendence/DailyReport endpoint
   - Filter by specific date
   - Shows all employees who attended on the selected date
   - Displays clock-in time, clock-out time, work hours, and overtime hours
   - Indicates if the day was a regular work day or holiday
   - Uses the EmpWorkHours model to source attendance data
   - Advanced export options:
     - Printer-friendly formatted reports with headers and footers
     - PDF export with custom formatting and RTL support
     - Excel export for data analysis
     - Optimized table display with DataTables

2. **Monthly Non-Attendance Report**
   - Access via the Attendence/MonthlyNonAttendanceReport endpoint
   - Filter by month and year
   - Shows all employees who were absent on each day of the selected month
   - Provides a summary of absences per day across the month
   - Includes a detailed breakdown of absences per employee
   - Calculates absence days per employee for the selected month
   - Color-coded indicators help distinguish absence patterns
   - Advanced export and printing features:
     - Selective printing of report sections (daily summary or employee summary)
     - Comprehensive PDF export with multiple sections and page breaks
     - Excel export focused on employee absence statistics
     - Custom print headers and footers with report metadata

3. **Personal Monthly Attendance Report**
   - Access via the Attendence/My/Attendance endpoint
   - Designed for individual employees to track their own attendance
   - Filter by month and year
   - Displays daily attendance records in a visually appealing card format
   - Shows clock-in time, clock-out time for each day
   - Features progress bars to visualize work hours (7 hours = 100%)
   - Color-coded progress bars:
     - Red: Less than 5.5 hours (< 78.6%)
     - Yellow: Between 5.5 and 6.5 hours (78.6% - 92.9%)
     - Green: 6.5 hours or more (≥ 92.9%)
   - Integrates excuse information directly in daily cards
   - Calculates and displays total excuse duration for the month
   - Shows detailed information for each excuse:
     - Excuse type (استئذان, تأخير, مهمة رسمية)
     - Start and end times
     - Duration
     - Reason
     - Approval status
   - Includes hour markers on progress bars for better time visualization
   - Print-friendly layout with appropriate headers and styling
   - Automatically excludes weekends (Friday and Saturday) from the report
   - Displays leave information when an employee is on approved leave:
     - Leave type (e.g., سنوية, عارضة, مرضية)
     - Leave duration in days
     - Leave start and end dates
     - Approval status (pending, approved, rejected, finalized)
   - Visual indicators for different day types:
     - Regular attendance days: blue/green progress bars
     - Leave days: blue border with leave type badge
     - Days with excuses: yellow border with excuse badge
     - Weekend days are excluded by default

4. **Implementation Notes**
   - All reports integrate with the existing EmpWorkHours model
   - Missing attendance records are interpreted as absences
   - Reports use DataTables for efficient sorting and searching
   - Print styles are included inline in the view for consistent formatting
   - Custom print layouts ensure optimal paper usage and readability
   - PDF generation uses pdfmake library with RTL support for Arabic text
   - Excel exports are optimized for data analysis with relevant columns
   - The MyAttendance view leverages Bootstrap's progress bars and card components
   - Excuses are pulled from the Absent table and matched to attendance dates
   - Leave data is pulled from the TleaveAplTx table with the following conditions:
     - CancelFlag = 0 (not canceled)
     - ReqStat = 1, 4, or 6 (approved, generated, or finalized)
     - Leave codes are retrieved from TleaveCode table
   - Weekend detection uses the IsWeekend helper method to identify Friday and Saturday
   - Holiday detection uses data from the Tholidays table to exclude official holidays

## Holiday Handling

The system handles holidays in the following way:

1. **Holiday Data Source**
   - Holidays are defined in the `Tholidays` table
   - Key fields:
     - `Holiday`: The date of the holiday
     - `DespA`: Holiday description in Arabic
     - `DespE`: Holiday description in English

2. **Holiday Integration**
   - Holidays are excluded from attendance calculation
   - Employees are not required to attend on holiday dates
   - Absence on holidays is not counted in statistics
   - Work days count excludes both weekends and holidays
   - Expected attendance is calculated only for working days

3. **Technical Implementation**
   ```csharp
   // Get holidays in the date range
   var holidays = _db.Tholidays
       .Where(h => h.Holiday.HasValue && 
               h.Holiday.Value.Date >= from.Value.Date && 
               h.Holiday.Value.Date <= to.Value.Date)
       .Select(h => h.Holiday.Value.Date)
       .ToList();
   
   // Count total work days (excluding weekends and holidays)
   int totalWorkDays = 0;
   for (var date = from.Value.Date; date <= to.Value.Date; date = date.AddDays(1))
   {
       if (date.DayOfWeek != DayOfWeek.Friday && 
           date.DayOfWeek != DayOfWeek.Saturday && 
           !holidays.Contains(date.Date))
       {
           totalWorkDays++;
       }
   }
   ```

## Related Documentation
# Attendance Reporting System

## Overview
The Attendance Reporting System provides comprehensive reports on employee attendance, including daily, monthly, and custom date range reports. The system supports filtering by organization structure (DG, Department, Section) and attendance status.

## Models

### EmpWorkHours
Stores the daily attendance records for employees, including:
- Employee number
- Date
- Check-in time
- Check-out time
- Total hours worked

### AttendanceWarning
Tracks when warning emails have been sent to employees regarding attendance issues:
- Employee number (EMP_NO)
- Attendance date (ATTENDENCE_DATE)
- These form a composite primary key

## Controllers

### AttendenceController
The main controller that handles attendance-related functionality, including:
- Daily attendance reports
- Monthly attendance reports
- Employee-specific attendance reports
- Comprehensive attendance reports with filtering
- Sending warning emails for attendance issues

## Key Features

### AttendanceReport
- Custom date range filtering
- Organizational structure filtering (DG, Department, Section)
- Status filtering (Complete, Incomplete, Absent)
- Statistical summary of attendance patterns
- Leave integration to show employees on leave
- Training integration to show employees in training
- Excuses integration to show approved absences
- Attendance time distribution charts showing:
  - Entry time distribution (before 7AM, 7-8AM, 8-9AM, after 9AM)
  - Exit time distribution (before 2PM, 2-3PM, 3-4PM, after 4PM)
  - Automated analysis of attendance patterns with percentage breakdowns
  - Identification of most common entry and exit times

### Warning Email System
- Send targeted warning emails to employees with attendance issues
- Track which warnings have been sent using the AttendanceWarning model
- Prevents duplicate warnings for the same issue
- Uses email templates for consistent messaging

## View Models

### AttendenceViewModel
Contains data and helper methods for attendance views, including:
- Attendance records
- Employee information
- Organizational structure
- Leave information
- Excuse information
- Status determination logic
- Warning tracking

## Implementation Notes

1. The AttendanceWarning table (TATTENEDENCE_WARNINGS) uses a composite primary key (EMP_NO, ATTENDENCE_DATE)
2. The system automatically checks if a warning has already been sent before displaying the warning button
3. When a warning is sent, a record is created in the AttendanceWarning table
4. Email templates are used for consistent messaging

# Attendance Reporting System

<!-- META: Human Resource Management System Attendance Reports Documentation -->
<!-- KEYWORDS: attendance, reports, time tracking, leave tracking, absence monitoring -->

## Overview

The Attendance Reporting System provides comprehensive tools for tracking and analyzing employee attendance data. It enables HR departments to monitor attendance patterns, identify attendance issues, and take appropriate actions.

## Available Reports

### 1. Daily Attendance Report

This report shows all attendance records for a specific day.

- **Access Path**: `/Attendence/DailyReport`
- **Parameters**:
  - `date`: The date to view attendance for (defaults to current day)
- **Features**:
  - List of all employees with their check-in and check-out times
  - Work hours calculation and visualization

### 2. Monthly Non-Attendance Report

This report identifies employees who did not attend work on specific days during a month.

- **Access Path**: `/Attendence/MonthlyNonAttendanceReport`
- **Parameters**:
  - `month`: Month number (1-12)
  - `year`: Year (4-digit)
- **Features**:
  - Calendar view showing attendance patterns
  - List of employees who were absent on each day

### 3. Comprehensive Attendance Report

This report provides detailed attendance information with extensive filtering options and actionable insights.

- **Access Path**: `/Attendence/AttendanceReport`
- **Parameters**:
  - `from`: Start date for the report period
  - `to`: End date for the report period
  - `Dg`: Directorate General code filter
  - `Dept`: Department code filter
  - `Section`: Section code filter
- **Features**:
  - Organizational unit filtering (DG, Department, Section)
  - Date range selection
  - Detail view of attendance records with check-in/out times
  - Leave status tracking
  - Excuse request tracking
  - Work hours visualization with colored indicators
  - Warning email system for attendance issues
  - Statistical summary:
    - Total work days in the period
    - Number of employees
    - Attendance records count
    - Employees on leave
    - Employees with excuses
    - Overall attendance percentage

### 4. My Attendance

Personal attendance report for individual employees to view their own attendance records.

- **Access Path**: `/Attendence/My`
- **Parameters**:
  - `month`: Month number (1-12)
  - `year`: Year (4-digit)
- **Features**:
  - Personal attendance history
  - Leave and excuse information
  - Work hours tracking

## Email Warning System

The system includes functionality to send warning emails to employees for attendance issues:

- **Email Format**: `{EmployeeNo}@mail.gsc.local`
- **Warning Types**:
  - Absence without approved leave
  - Insufficient work hours (less than 5.5 hours)
- **Implementation**:
  - Modal-based email composition interface
  - Template system with date placeholders
  - AJAX-based email sending process

### Bulk Warning System

The bulk warning system allows HR administrators to send warning notifications to multiple employees based on attendance status criteria:

- **Background Processing**: Uses Task.Run for non-blocking execution to handle large datasets (5,000+ records)
- **Status-Based Filtering**: Evaluates attendance records using approval flags rather than string comparisons
- **Progress Tracking**: Real-time progress updates via AJAX polling during bulk processing
- **Performance Optimization**: Batch processing with configurable batch sizes for optimal performance
- **Error Handling**: Comprehensive error logging and graceful failure handling for individual email failures

#### Bulk Warning Features

- **Criteria-Based Selection**:
  - Employees with incomplete attendance (< 6 hours)
  - Employees with absence without approved leave
  - Date range filtering for targeted warnings
  - Organizational filtering (DG, Department, Section)

- **Background Processing Architecture**:
  - Non-blocking UI during bulk operations
  - Progress tracking with completion percentage
  - Individual email failure tracking
  - Automatic retry mechanism for failed emails

- **Performance Considerations**:
  - Batch size: 50 employees per batch (configurable)
  - Processing delay: 100ms between batches to prevent server overload
  - Memory optimization: Streaming data processing for large datasets
  - Database connection pooling for concurrent operations

### Email Service Integration

The attendance warning system integrates with the organization's EmailService to streamline email communications:

- **Service Integration**:
  - Uses dependency injection to inject `EmailService` into `AttendenceController`
  - Leverages existing email infrastructure for reliable delivery
  - Maintains consistent email branding across the system

- **Template-Based Emails**:
  - Template name: `attendance.warning`
  - Template location: `Views/EmailTemplates/attendance.warning.cshtml`
  - Template variables:
    - `employeeName`: Employee's full name
    - `date`: Date of absence or incomplete attendance
    - `department`: Employee's department name

- **Auto-Registration**:
  - Template is automatically registered in the `AttendenceController` constructor if it doesn't exist
  - Uses file system to read the template content from the Views directory
  - Seamlessly falls back to plain text emails if template is unavailable

- **Email Sending Process**:
  1. HR user clicks "Send Warning" button for an employee with attendance issues
  2. Modal dialog appears with pre-filled email template
  3. User can customize the message if needed
  4. AJAX request is sent to `SendWarningEmail` endpoint
  5. Controller retrieves employee information
  6. Email is sent using `EmailService.SendTemplatedEmailAsync` method
  7. Success/failure feedback is provided to the user

- **Technical Implementation**:
  ```csharp
  [HttpPost("SendWarningEmail")]
  public async Task<IActionResult> SendWarningEmail(int warningEmpNo, string warningDate,
      string warningSubject, string warningMessage)
  {
      // Get employee information
      var employee = _db.VempDtls.FirstOrDefault(e => e.EmpNo == warningEmpNo);

      // Generate email address
      string emailAddress = $"{warningEmpNo}@mail.gsc.local";

      // Create variables dictionary for email template
      var variables = new Dictionary<string, string> {
          { "employeeName", employee.EmpNameA },
          { "date", warningDate },
          { "department", employee.DeptDespA ?? "الإدارة" }
      };

      // Send email using template if available
      await _emailService.SendTemplatedEmailAsync(
          emailAddress,
          warningSubject,
          "attendance.warning",
          variables
      );
  }
  ```

#### Bulk Warning Technical Implementation

- **Controller Action**: `SendBulkWarningEmails`
  ```csharp
  [HttpPost("SendBulkWarningEmails")]
  public async Task<IActionResult> SendBulkWarningEmails(DateTime fromDate, DateTime toDate,
      int dgCode = 0, int deptCode = -1, int sectionCode = 0, int statusFilter = 3)
  {
      // Start background processing
      var jobId = Guid.NewGuid().ToString();

      // Store job status for progress tracking
      _cache.Set($"bulk_warning_job_{jobId}", new BulkWarningJobStatus
      {
          Status = "Processing",
          Progress = 0,
          TotalEmployees = 0,
          ProcessedEmployees = 0
      });

      // Start background task
      _ = Task.Run(async () => await ProcessBulkWarnings(jobId, fromDate, toDate,
          dgCode, deptCode, sectionCode, statusFilter));

      return Json(new { success = true, jobId = jobId });
  }
  ```

- **Background Processing Method**: `ProcessBulkWarnings`
  - Implements batch processing for optimal performance
  - Uses status-based filtering with approval flags
  - Provides progress tracking and error handling
  - Prevents duplicate warnings using AttendanceWarning table

- **Progress Tracking**: `GetBulkWarningProgress`
  ```csharp
  [HttpGet("GetBulkWarningProgress/{jobId}")]
  public IActionResult GetBulkWarningProgress(string jobId)
  {
      var status = _cache.Get<BulkWarningJobStatus>($"bulk_warning_job_{jobId}");
      return Json(status ?? new BulkWarningJobStatus { Status = "NotFound" });
  }
  ```

## Filtering System

The reporting system implements the organization's standard filtering pattern as defined in [filtering-system.md](../filtering-system.md):

- **Hierarchical Filtering**:
  - Directorate General (DG) filtering
  - Department filtering
  - Section filtering
- **Date Range Filtering**:
  - Start date
  - End date
- **Cascading Dropdowns**:
  - DG selection filters available departments
  - Department selection filters available sections

## Data Processing Logic

### Attendance Status Determination

The system applies the following rules to determine an employee's attendance status for a day:

1. If the employee has an approved leave application that includes the date, they are marked as "On Leave"
2. If the employee has an attendance record with work hours:
   - Less than 5.5 hours: "Incomplete Attendance"
   - 5.5 hours or more: "Complete Attendance"
3. If the employee has an excuse record for the date, "Excused" status is added
4. If no attendance record exists and no leave is recorded, the employee is marked as "Absent"

### Work Hours Calculation

Work hours are calculated as the difference between check-in and check-out times. The progress bar visualization follows these guidelines:

- Less than 5.5 hours: Red (incomplete day)
- 5.5 to 6.5 hours: Yellow (partial day)
- More than 6.5 hours: Green (complete day)

### Weekend and Holiday Handling

The system skips both weekends (Friday and Saturday) and official holidays when calculating attendance statistics and expected work days:

- **Weekend Detection**: Uses the `IsWeekend` helper method to identify Friday and Saturday
- **Holiday Detection**: Uses the `IsHoliday` helper method which checks against the Tholidays table
- **Workday Detection**: Uses the `IsWorkday` helper method which returns true only if the date is neither a weekend nor a holiday

## Technical Implementation

### Controller Classes

- **AttendenceController.cs**: Main controller handling all attendance report actions
- **Key Actions**:
  - `DailyReport`: Shows attendance for a specific day
  - `MonthlyNonAttendanceReport`: Shows monthly attendance summary
  - `AttendanceReport`: Comprehensive attendance reporting with filtering
  - `MyAttendance`: Personal attendance view
  - `SendWarningEmail`: Handles sending warning emails

### View Model

- **AttendenceViewModel.cs**: Contains all data and helper methods needed for attendance reports
- **Holiday-Related Properties and Methods**:
  - `Holidays`: List of DateTime objects representing holidays in the selected date range
  - `IsHoliday(DateTime date)`: Checks if a given date is a holiday
  - `IsWorkday(DateTime date)`: Checks if a given date is a workday (not weekend and not holiday)

### Database Entities

- **EmpWorkHours**: Stores individual attendance records with check-in/out times
- **TleaveAplTx**: Stores approved leave applications
- **Absent**: Stores excuse requests
- **Tholidays**: Stores official holidays

## Maintenance and Future Enhancements

### Planned Enhancements

1. Automatic email notifications for HR about attendance issues
2. Department-level summary reports for managers
3. API integration for mobile attendance tracking
4. Trend analysis for attendance patterns over time

### Maintenance Tasks

1. SMTP server configuration for email sending
2. Database indexing for performance optimization
3. Regular review of attendance calculation rules
4. Regular maintenance of the holiday calendar in Tholidays 